{"version": "0.2.0", "configurations": [{"name": "Python: <PERSON><PERSON>", "type": "debugpy", "request": "launch", "module": "hmmer", "console": "integratedTerminal", "subProcess": true, "justMyCode": true}, {"name": "Python: Downloader", "type": "debugpy", "request": "launch", "module": "downloader", "console": "integratedTerminal", "justMyCode": true}, {"name": "Python: Executor", "type": "debugpy", "request": "launch", "module": "executor", "console": "integratedTerminal", "justMyCode": false}, {"name": "Python: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": ["-Xfrozen_modules=off"]}, {"name": "Containers: Python - General", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "python": {"pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}], "projectType": "general"}}]}