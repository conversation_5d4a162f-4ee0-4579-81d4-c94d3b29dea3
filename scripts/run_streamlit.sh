#!/bin/bash

# Set default values
PORT=8501
HOST="localhost"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --port)
      PORT="$2"
      shift 2
      ;;
    --host)
      HOST="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Get the project root directory (where the scripts directory is located)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Set PYTHONPATH to include the project root
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# Run Streamlit
echo "Starting Streamlit server on http://$HOST:$PORT"
streamlit run "$PROJECT_ROOT/src/hmmer/streamlit/app.py" --server.port "$PORT" --server.address "$HOST"
