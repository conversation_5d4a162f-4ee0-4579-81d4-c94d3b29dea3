#!/bin/bash

# Script to run hmmer and log output to a timestamped file
cd /home/<USER>/projects/hmmer
# Create logs directory if it doesn't exist
mkdir -p logs

# Generate timestamp for the log file (format: YYYY-MM-DD_HHMMSS)
TIMESTAMP=$(date +"%Y-%m-%d_%H%M%S")

# Print start message
echo "Starting hmmer at $(date)"

# Run hmmer and redirect both stdout and stderr to the log file
# activate enivronment
source /home/<USER>/projects/hmmer/.venv/bin/activate

# run hmmer
LOG_FILE="logs/hmmer_${TIMESTAMP}.log"
echo "Logging output to ${LOG_FILE}"
/home/<USER>/projects/hmmer/.venv/bin/python -m hmmer >> "$LOG_FILE" 2>&1

# Check if the command was successful
if [ $? -eq 0 ]; then
    echo "hmmer completed successfully at $(date)"
else
    echo "hmmer failed with exit code $? at $(date)"
fi

echo "Log file: ${LOG_FILE}"

