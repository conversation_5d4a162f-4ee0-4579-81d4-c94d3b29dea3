@echo off
REM Script to run the HMMER Streamlit server on Windows

REM Set default values
set PORT=8501
set HOST=localhost

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :run
if "%~1"=="--port" (
    set PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--host" (
    set HOST=%~2
    shift
    shift
    goto :parse_args
)
echo Unknown option: %~1
exit /b 1

:run
REM Get the project root directory (where the scripts directory is located)
for %%i in ("%~dp0..") do set "PROJECT_ROOT=%%~fi"

REM Run Streamlit using Poetry
echo Starting HMMER Streamlit server on http://%HOST%:%PORT%
cd "%PROJECT_ROOT%" && poetry run hmmer-streamlit --port %PORT% --host %HOST%

REM If Poetry is not available, try running directly
if %ERRORLEVEL% neq 0 (
    echo Poetry not found, trying to run directly...
    cd "%PROJECT_ROOT%" && python -m hmmer.streamlit.run_streamlit --port %PORT% --host %HOST%
)
