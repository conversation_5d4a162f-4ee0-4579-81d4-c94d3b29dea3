import pickle
# import pandas as pd
from sqlalchemy import create_engine

import tradertools as tt
from hmmer.st_mprocess_slowloop import run

engine = create_engine(tt.db.pg().conn_string)


prods = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'KC', 'SB']
# prods = ['GC']
results = {}
for p in prods:
    print(f"Running {p}")
    try:
        results[p] = run(p, engine)
        with open(f"./tests/data/results_{p}.pkl", "wb") as f:
            pickle.dump(results[p], f)
    except Exception as e:
        print(f"Error running {p}: {e}")
        results[p] = None

