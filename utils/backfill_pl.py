import os
from datetime import datetime, date, time
from zoneinfo import ZoneInfo
import pandas as pd

import tradertools as tt

PG_CLIENT = os.getenv("POSTGRES_CLIENT")
TZ = ZoneInfo("America/New_York")


pg = tt.clients.get_pg_client('postgres_prod')

all_fills = pg.query("SELECT * FROM hmmer.fills")
 
start_date = date.today()

prds = dict[str, list]()

for f in all_fills:
    td =  f['dt'].date()
    if td < start_date:
        start_date = td
    prd = f['prd']
    if prd not in prds:
        prds[prd] = []
    prds[prd].append(f)

start_dt = datetime.combine(start_date, time(9, 30, 0)).replace(tzinfo=ZoneInfo("America/New_York"))


pl_data = []

for prd in prds:
    inst = tt.instruments.get_definition(product=prd)
    pv = float(inst.pointValue)
    
    allbars = pd.DataFrame(pg.query(f"SELECT * FROM hmmer.allbars WHERE prd = '{prd}' AND dt >= '{start_dt}' ORDER BY dt")).sort_values(['dt', 'prd'])
    allbars['dt'] = allbars['dt'].apply(lambda x: x.astimezone(TZ))
    allbars['et'] = allbars['et'].apply(lambda x: x.astimezone(TZ))
    # filtered_allbars = allbars[allbars['et'].dt.time >= time(17, 30)].groupby('TradeDate').first().reset_index()
    filtered_allbars = allbars.groupby('TradeDate').last().reset_index()
    filtered_allbars['Open'] = filtered_allbars['Close'].shift(1)
    filtered_allbars = filtered_allbars[['TradeDate', 'name2', 'Open', 'Close']].copy()
    
    fills = prds[prd]
    
    # Initialize position tracking - THESE PERSIST ACROSS DAYS
    position = 0
    totalPL = 0
    totalFees = 0
    
    # Process fills to build position history
    fill_df = pd.DataFrame(fills)
    fill_df['trade_date'] = fill_df['dt'].apply(lambda x: x.date())
    
    for _, row in filtered_allbars.iterrows():
        trade_date = row['TradeDate']
        name2 = row['name2']
        open_price = row['Open']
        close_price = row['Close']
         
        day_fills = fill_df[fill_df['trade_date'] == trade_date]
        day_pl = 0
        day_fees  = 0

        buy_notional = position * open_price if position > 0 else 0.0
        sell_notional = -position * open_price if position < 0 else 0.0
        
        for _, fill in day_fills.iterrows():
            fill_qty = fill['qty']
            fill_price = fill['price']
            side = fill['side']
            if side == 'BUY':
                buy_notional += fill_qty * fill_price
            else:
                sell_notional += fill_qty * fill_price    
            position += fill_qty if side == 'BUY' else -fill_qty
            
        if position > 0:
            sell_notional += position * close_price
        elif position < 0:
            buy_notional += -position * close_price
        
        day_pl = (sell_notional - buy_notional) * pv
        day_fees = day_fills['fees'].sum()
        
        
        if day_pl != day_pl:
            day_pl = 0.0
            
        if day_pl == -0.0:
            day_pl = 0.0
        
        totalPL += day_pl
        totalFees += day_fees
        
        pl_data.append({
            'tradeDate': trade_date,
            'product': prd,
            'name2': name2,
            'open': open_price,
            'close': close_price,
            'position': round(position, 8),
            'dailyPL': round(day_pl, 2),
            'dailyFees': round(day_fees, 2),
            # 'totalFees': round(totalFees, 2)
        })
        
# Convert to DataFrame for easier analysis
pl_df = pd.DataFrame(pl_data).sort_values(['tradeDate', 'product'])
records = pl_df.to_dict('records')
pg.upsert('hmmer.performance', records, conflict_target=['tradeDate', 'product'])


ts = pl_df.pivot(columns='product', index='tradeDate', values='dailyPL').ffill().cumsum()