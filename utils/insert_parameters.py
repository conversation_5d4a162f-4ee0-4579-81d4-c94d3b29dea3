import pandas as pd
import numpy as np
import tradertools as tt

from hmmer.st_mprocess_modelparams import params_dict, parse_params


ctab = pd.DataFrame({
    'product': ['CL', 'NG', 'ZM', 'KE', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'ZB', 'KC', 'SB'],
    'tas_est': ['14:30', '14:30', '14:15', '14:15', '14:15', '14:15', '14:15',
                '13:00', '13:30', '16:00', '15:00', '13:30', '13:00'],
    'shiftdays': [-15, -15, -5, -5, -5, -5, -5, -5, -5, 15, -5, -5, -5]
})

multiplier_df = pd.DataFrame({
    'prd': ['CL','NG','HG','GC','NQ','ZS','Z<PERSON>','Z<PERSON>','SB','KC'],
    'multiplier': [1000,10000,25000,100,20,50,50,50,1120,375],
    'size':[1,1,1,1,1,1,2,3,3,1]
})


table = []
for _, row in ctab.iterrows():
    product = row['product']
    tas_est = row['tas_est']
    shiftdays = row['shiftdays']
    nrow = {}
    nrow['product'] = product
    nrow['tas_est'] = tas_est
    nrow['shiftdays'] = shiftdays
    if product in params_dict:
        nrow['multiplier'] = multiplier_df[multiplier_df['prd'] == product]['multiplier'].values[0]
        nrow['size'] = multiplier_df[multiplier_df['prd'] == product]['size'].values[0]
        params = params_dict[product]
        params = parse_params(params)
        nrow.update(params)
    table.append(nrow)
df = pd.DataFrame(table)
df.replace(np.nan, None, inplace=True)
df.dropna(inplace=True)



table = df.to_dict(orient='records')

tt.db.pg().insert('hmmer.parameters', table)
