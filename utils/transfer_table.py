#!/usr/bin/env python3
"""
PostgreSQL table copy script using psycopg3
Copies table structure and data from source to destination server
"""

import sys
from typing import Optional

import tradertools as tt

# source = tt.clients.get_pg_client('postgres_dev')
# dest = tt.clients.get_pg_client('postgres_prod')


COPY_DATA = True
# Table to copy (modify as needed)
SCHEMA_TABLES = [ # format: schema.table or just table
    # "hmmer_bars.CL_30min",
    # "hmmer_bars.GC_30min",
    # "hmmer_bars.HG_30min",
    # "hmmer_bars.KC_30min",
    # "hmmer_bars.NQ_30min",
    # "hmmer_bars.SB_30min",
    # "hmmer_bars.ZC_30min",
    # "hmmer_bars.ZS_30min",
    # "hmmer_bars.ZW_30min",
    "hmmer_bars.NG_30min",
]  


def parse_table_name(schema_table: str) -> tuple[Optional[str], str]:
    """Parse schema.table format into schema and table components"""
    if '.' in schema_table:
        schema, table = schema_table.split('.', 1)
        return schema, table
    return None, schema_table


def get_table_ddl(conn, schema: Optional[str], table: str) -> str:
    """Extract CREATE TABLE statement from source database"""
    if schema:
        # ...existing code...
        full_table_name = f'"{schema}"."{table}"'
    else:
        # ...existing code...
        full_table_name = f'"{table}"'
        
    # Get table structure with better array type handling
    if schema:
        table_query = """
            SELECT 
                c.column_name, 
                CASE 
                    WHEN c.data_type = 'ARRAY' THEN 
                        CASE 
                            WHEN c.udt_name = '_text' THEN 'TEXT[]'
                            WHEN c.udt_name = '_varchar' THEN 'VARCHAR[]'
                            WHEN c.udt_name = '_int4' THEN 'INTEGER[]'
                            WHEN c.udt_name = '_int8' THEN 'BIGINT[]'
                            WHEN c.udt_name = '_float8' THEN 'DOUBLE PRECISION[]'
                            WHEN c.udt_name = '_bool' THEN 'BOOLEAN[]'
                            WHEN c.udt_name = '_date' THEN 'DATE[]'
                            WHEN c.udt_name = '_timestamp' THEN 'TIMESTAMP[]'
                            WHEN c.udt_name = '_timestamptz' THEN 'TIMESTAMPTZ[]'
                            WHEN c.udt_name = '_jsonb' THEN 'JSONB[]'
                            WHEN c.udt_name = '_json' THEN 'JSON[]'
                            ELSE c.udt_name  -- fallback to the actual type name
                        END
                    ELSE c.data_type
                END as data_type,
                c.character_maximum_length, 
                c.is_nullable, 
                c.column_default, 
                c.numeric_precision, 
                c.numeric_scale,
                c.udt_name
            FROM information_schema.columns c
            WHERE c.table_schema = %s AND c.table_name = %s
            ORDER BY c.ordinal_position
        """
        params = (schema, table)
        # full_table_name = f'{schema}."{table}"'
    else:
        table_query = """
            SELECT 
                c.column_name, 
                CASE 
                    WHEN c.data_type = 'ARRAY' THEN 
                        CASE 
                            WHEN c.udt_name = '_text' THEN 'TEXT[]'
                            WHEN c.udt_name = '_varchar' THEN 'VARCHAR[]'
                            WHEN c.udt_name = '_int4' THEN 'INTEGER[]'
                            WHEN c.udt_name = '_int8' THEN 'BIGINT[]'
                            WHEN c.udt_name = '_float8' THEN 'DOUBLE PRECISION[]'
                            WHEN c.udt_name = '_bool' THEN 'BOOLEAN[]'
                            WHEN c.udt_name = '_date' THEN 'DATE[]'
                            WHEN c.udt_name = '_timestamp' THEN 'TIMESTAMP[]'
                            WHEN c.udt_name = '_timestamptz' THEN 'TIMESTAMPTZ[]'
                            WHEN c.udt_name = '_jsonb' THEN 'JSONB[]'
                            WHEN c.udt_name = '_json' THEN 'JSON[]'
                            ELSE c.udt_name  -- fallback to the actual type name
                        END
                    ELSE c.data_type
                END as data_type,
                c.character_maximum_length, 
                c.is_nullable, 
                c.column_default, 
                c.numeric_precision, 
                c.numeric_scale,
                c.udt_name
            FROM information_schema.columns c
            WHERE c.table_name = %s AND c.table_schema = 'public'
            ORDER BY c.ordinal_position
        """
        params = (table,)
        full_table_name = table
    
    with conn.cursor() as cur:
        cur.execute(table_query, params)
        columns = cur.fetchall()
        
        if not columns:
            raise ValueError(f"Table {full_table_name} not found")
        
        # Build CREATE TABLE statement
        ddl_parts = []
        for col in columns:
            col_name, data_type, char_max_len, nullable, default, num_precision, num_scale, udt_name = col
            
            # Build column definition
            col_def = f'"{col_name}" {data_type.upper()}'
            
            # Add length/precision for specific types (skip for arrays)
            if not data_type.endswith('[]'):
                if data_type in ('character', 'char', 'bpchar') and char_max_len:
                    col_def += f'({char_max_len})'
                elif data_type in ('character varying', 'varchar') and char_max_len:
                    col_def += f'({char_max_len})'
                elif data_type == 'numeric' and num_precision:
                    if num_scale:
                        col_def += f'({num_precision},{num_scale})'
                    else:
                        col_def += f'({num_precision})'
            
            # Add NOT NULL constraint
            if nullable == 'NO':
                col_def += ' NOT NULL'
                
            # Add default value
            if default:
                col_def += f' DEFAULT {default}'
            
            ddl_parts.append(col_def)
        
        # Get primary key constraints
        pk_query = """
            SELECT string_agg('"' || column_name || '"', ', ' ORDER BY ordinal_position)
            FROM information_schema.key_column_usage k
            JOIN information_schema.table_constraints t
                ON k.constraint_name = t.constraint_name
            WHERE t.constraint_type = 'PRIMARY KEY'
                AND t.table_schema = %s AND t.table_name = %s
        """ if schema else """
            SELECT string_agg('"' || column_name || '"', ', ' ORDER BY ordinal_position)
            FROM information_schema.key_column_usage k
            JOIN information_schema.table_constraints t
                ON k.constraint_name = t.constraint_name
            WHERE t.constraint_type = 'PRIMARY KEY'
                AND t.table_schema = 'public' AND t.table_name = %s
        """
        
        cur.execute(pk_query, params)
        pk_result = cur.fetchone()
        
        if pk_result and pk_result[0]:
            constraint_name = f"{table}_pkey"
            ddl_parts.append(f'CONSTRAINT {constraint_name} PRIMARY KEY ({pk_result[0]})')
        
        create_table_sql = f"""
        CREATE TABLE {full_table_name} (
            {',\n    '.join(ddl_parts)}
        );
        """
        
        return create_table_sql


def copy_table_data(source_conn, dest_conn, schema: Optional[str], table: str, batch_size: int = 1000):
    """Copy data from source to destination table"""
    
    full_table_name = f'"{schema}"."{table}"' if schema else f'"{table}"'
    
    # Get total row count for progress tracking
    count_query = f"SELECT COUNT(*) FROM {full_table_name}"
    with source_conn.cursor() as cur:
        cur.execute(count_query)
        total_rows = cur.fetchone()[0]
    
    print(f"Copying {total_rows:,} rows...")
    
    # Get column names and types
    if schema:
        col_query = """
            SELECT column_name, data_type, udt_name
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = %s
            ORDER BY ordinal_position
        """
        params = (schema, table)
    else:
        col_query = """
            SELECT column_name, data_type, udt_name
            FROM information_schema.columns 
            WHERE table_name = %s AND table_schema = 'public'
            ORDER BY ordinal_position
        """
        params = (table,)
    
    with source_conn.cursor() as cur:
        cur.execute(col_query, params)
        column_info = cur.fetchall()
    
    columns = [col[0] for col in column_info]
    column_types = {col[0]: (col[1], col[2]) for col in column_info}
    
    column_list = ', '.join(f'"{col}"' for col in columns)
    placeholders = ', '.join(['%s'] * len(columns))
    
    insert_query = f"""
        INSERT INTO {full_table_name} ({column_list}) 
        VALUES ({placeholders})
    """
    
    # Copy data in batches
    copied_rows = 0
    with source_conn.cursor() as source_cur:
        source_cur.execute(f"SELECT {column_list} FROM {full_table_name}")
        
        with dest_conn.cursor() as dest_cur:
            while True:
                rows = source_cur.fetchmany(batch_size)
                if not rows:
                    break
                
                # Process rows to handle special data types
                processed_rows = []
                for row in rows:
                    processed_row = []
                    for i, value in enumerate(row):
                        col_name = columns[i]
                        data_type, udt_name = column_types[col_name]
                        
                        # Handle JSONB/JSON columns
                        if data_type.lower() in ('jsonb', 'json') and value is not None:
                            if isinstance(value, (dict, list)):
                                # Convert Python dict/list to JSON string
                                import json
                                processed_row.append(json.dumps(value))
                            else:
                                processed_row.append(value)
                        # Handle array columns
                        elif data_type == 'ARRAY' and value is not None:
                            if isinstance(value, list):
                                processed_row.append(value)  # psycopg3 handles lists for arrays
                            else:
                                processed_row.append(value)
                        else:
                            processed_row.append(value)
                    
                    processed_rows.append(processed_row)
                
                dest_cur.executemany(insert_query, processed_rows)
                copied_rows += len(rows)
                
                if copied_rows % (batch_size * 10) == 0:  # Progress every 10 batches
                    print(f"Copied {copied_rows:,}/{total_rows:,} rows ({copied_rows/total_rows*100:.1f}%)")
    
    print(f"✓ Successfully copied {copied_rows:,} rows")
    
    
    

def main():
    for schema_table in SCHEMA_TABLES:
        
        source = tt.clients.get_pg_client('postgres_dev')
        dest = tt.clients.get_pg_client('postgres_prod')
        
        schema, table = parse_table_name(schema_table)
        
        print(f"Copying table: {schema_table}")
        print(f"Schema: {schema or 'public'}")
        print(f"Table: {table}")
        print("-" * 50)
        
        try:
            # Connect to source database
            print("Connecting to source database...")
            
            with source.sync_conn as source_conn:
                # Connect to destination database  
                print("Connecting to destination database...")
                with dest.sync_conn as dest_conn:
                    
                    # Get table DDL from source
                    print("Extracting table structure...")
                    create_table_sql = get_table_ddl(source_conn, schema, table)
                    
                    print("Creating table structure:")
                    print(create_table_sql)
                    
                    # Create schema if it doesn't exist
                    if schema:
                        with dest_conn.cursor() as cur:
                            cur.execute(f"CREATE SCHEMA IF NOT EXISTS {schema}")
                    
                    # Drop table if exists and create new one
                    full_table_name = f'"{schema}"."{table}"' if schema else f'"{table}"'
                    with dest_conn.cursor() as cur:
                        cur.execute(f"DROP TABLE IF EXISTS {full_table_name}")
                        cur.execute(create_table_sql)
                    
                    dest_conn.commit()
                    print("✓ Table structure created successfully")
                    
                    # Copy data
                    if COPY_DATA:
                        print("Starting data copy...")
                        copy_table_data(source_conn, dest_conn, schema, table)
                        dest_conn.commit()
                        print("✓ Data copy completed successfully")
                    
        except Exception as e:
            print(f"Error: {e}", file=sys.stderr)
            sys.exit(1)


if __name__ == "__main__":
    main()