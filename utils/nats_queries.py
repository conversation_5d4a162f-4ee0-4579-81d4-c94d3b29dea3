# %%
import pandas as pd
import tradertools as tt
# from datetime import datetime
# from zoneinfo import ZoneInfo

nats = tt.clients.get_nats_client("nats_dev")
print(nats)
func = 'summary'
func = 'summary_products'
func = 'summary_contracts'
# func = 'contract_reset_attempts'
# func = 'summary_product_performance'
msg = nats.request('strategies.hmmer.requests', func)

df = pd.DataFrame(msg)
df

df.sort_values('name')

nats.get_kv('hmmer', 'state.contracts.CLX25')
nats.get_kv('hmmer', 'state.products.ZW')



nats.delete_kv('hmmer', 'state.products.CL')
nats.delete_kv('hmmer', 'state.products.HG')
nats.delete_kv('hmmer', 'state.products.GC')
nats.delete_kv('hmmer', 'state.products.NQ')
nats.delete_kv('hmmer', 'state.products.NG')

nats.delete_kv('hmmer', 'state.products.ZC')
nats.delete_kv('hmmer', 'state.products.ZS')
nats.delete_kv('hmmer', 'state.products.ZW')

nats.put_kv('hmmer', 'state.products.CL', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.HG', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.GC', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.NQ', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.NG', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.ZC', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.ZS', {'regime_latch': False})
nats.put_kv('hmmer', 'state.products.ZW', {'regime_latch': False})




s = {'initiate_attempts': 0, 'stopadjtrail_attempts': 0, 'close_attempts': 0, 'can_trade': True, 'can_initiate': True}

nats.put_kv('hmmer', 'state.contracts.NGV25', s)

nats.get_kv('heartbeats', 'hmmer_executor')
nats.get_bucket('heartbeats')


FLOW_URL="https://defaultcd60f73cc312432999743a7264ccbf.8c.environment.api.powerplatform.com:443/powerautomate/automations/direct/workflows/68790eb3266243538d9fddf43803e49d/triggers/manual/paths/invoke/?api-version=1&sp"