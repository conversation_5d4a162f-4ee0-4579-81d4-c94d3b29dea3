import time

import pandas as pd
from sqlalchemy import create_engine, text

import tradertools as tt

engine = create_engine(tt.db.pg().conn_string)


product = "gc"
query = f"SELECT * FROM barchart_dumps.{product}_30min"

# time pandas and sqlalchemy to read the data into a dataframe
start = time.time()
df = pd.read_sql(query, engine)
end = time.time()
psql_time = end - start

# time sqlachemy to read the data into a dataframe
start = time.time()
with engine.connect() as conn:
    df = pd.DataFrame(conn.execute(text(query)).fetchall())
end = time.time()
sqlalchemy_time = end - start

# time tradertools to read the data into a dataframe
start = time.time()
df = pd.DataFrame(tt.db.pg().query(query))
end = time.time()
tradertools_time = end - start

print(f"query: {query}")
print(f"pandas     : {psql_time:5.2f}s")
print(f"sqlalchemy : {sqlalchemy_time:5.2f}s")
print(f"tradertools: {tradertools_time:5.2f}s")
