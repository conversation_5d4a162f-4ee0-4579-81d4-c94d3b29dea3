import os
import pandas as pd
import tradertools as tt

PG_CLIENT = os.getenv("POSTGRES_CLIENT")

pg = tt.clients.get_pg_client(PG_CLIENT)
SCHEMA = 'hmmer_bars'

products = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'KC', 'SB']
timeframes = ['5min', '30min', '240min']

tables = [r['table_name'] for r in pg.list_tables(SCHEMA)]

instdefs = [t.name2 for t in tt.instruments.get_definitions(include_expired=False)]

latests = []
for t in tables:
    query = (
        f"SELECT name2, MAX(dt) FROM {SCHEMA}.\"{t}\" "
        "GROUP BY name2 "
        "ORDER BY name2"
    )
    latests.extend(pg.query(query))

df = pd.DataFrame(latests)
df = df[df['name2'].isin(instdefs)]

df['max'] = df['max'].dt.tz_convert('America/Chicago')


third = df.set_index("name2").copy()

# first = df.copy()
# print(first)

# first.columns = ['first']
# second.columns = ['second']
# third.columns = ['third']

# df = first.merge(second.merge(third, left_index=True, right_index=True), left_index=True, right_index=True)

name = 'CLN25'

df.loc[name]
