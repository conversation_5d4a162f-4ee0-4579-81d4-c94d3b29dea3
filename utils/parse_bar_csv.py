import os
from dataclasses import dataclass
from datetime import datetime, timezone

import psycopg.errors

import tradertools as tt



month_codes = {
    'F': '01',
    'G': '02',
    'H': '03',
    'J': '04',
    'K': '05',
    'M': '06',
    'N': '07',
    'Q': '08',
    'U': '09',
    'V': '10',
    'X': '11',
    'Z': '12',
}

@dataclass
class Bar:
    dt: datetime
    name: str
    name2: str
    expiry: int
    open: float
    high: float
    low: float
    close: float
    volume: int
    
    
def parse_name_from_filename(filename):
    filename = filename.replace('.csv', '')
    name2, _ = filename.split('_')
    root = name2[:-3]
    name = root + name2[-3] + name2[-1]
    mnth = name2[-3]
    expiry = int('20' + name2[-2:] + month_codes[mnth])
    return root, name2, name, expiry

d = '/mnt/c/Users/<USER>/JA Technologies IV ApS/<PERSON> Whitt - LuneFiles/FuturesData/barchart/futs_csvs'

timeframes = ['5min','30min', '240min']
finished = ['cl', 'es', 'gc', 'he', 'hg', 'kc', 'ke', 'le', 'ng', 'nq', 'sb', 'zb' 'zc']
finished = [f + '_5min' for f in finished]

for timeframe in timeframes:
    new_d = os.path.join(d, timeframe)
    root_tf_dirs = os.listdir(new_d)
    for root_tf_dir in root_tf_dirs:
        print(root_tf_dir)
        files = os.listdir(os.path.join(new_d, root_tf_dir))
        for fname in files:
            print(f'opening {fname}')
            raw = []
            root, name2, name, expiry = parse_name_from_filename(fname)
            tname = f'{root.lower()}_{timeframe}'
            table_name = f'barchart_dumps.{tname}'
            if tname in finished:
                continue
            with open(os.path.join(new_d, root_tf_dir, fname), 'r') as f:
                lines = f.read().split('\n')
                for line in lines[1:]:
                    row = line.split(',')
                    if len(row) < 6:
                        continue
                    dt = datetime.strptime(row[0], '%Y-%m-%d %H:%M:%S')
                    dt = dt.replace(tzinfo=timezone.utc)
                    # bar = Bar(dt, name, name2, expiry, float(row[1]), float(row[2]), float(row[3]), float(row[4]), int(row[5]))
                    bar = {
                        'dt': dt,
                        'name': name,
                        'name2': name2,
                        'expiry': expiry,
                        'open': float(row[1]),
                        'high': float(row[2]),
                        'low': float(row[3]),
                        'close': float(row[4]),
                        'volume': int(row[5])
                    }
                    raw.append(bar)
            # df = pd.DataFrame(raw)
            try:
                print(f'writing {len(raw)} rows to {table_name}')
                tt.db.pg().insert_bulk(table_name, raw, chunk_size=10000)
                print(f'inserted {len(raw)} rows to {table_name}')
            except psycopg.errors.UndefinedTable:
                print(f'creating table {table_name}')
                create_table_query = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    dt TIMESTAMP WITH TIME ZONE,
                    name VARCHAR(32),
                    name2 VARCHAR(32),
                    expiry INTEGER,
                    open FLOAT,
                    high FLOAT,
                    low FLOAT,
                    close FLOAT,
                    volume INTEGER,
                    CONSTRAINT {table_name.replace('.', '_')}_dt_name2_unique UNIQUE (dt, name2)
                )
                """
                tt.db.pg().execute(create_table_query)
                # Then retry insertion
                tt.db.pg().insert_bulk(table_name, raw, chunk_size=10000)
                print(f'inserted {len(raw)} rows to {table_name}')
            except Exception as e:
                # Handle other exceptions
                print(f"Unexpected error: {e.__class__.__name__}: {e}")
                break
