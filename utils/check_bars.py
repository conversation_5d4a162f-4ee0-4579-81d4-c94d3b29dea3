# %%
import os
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

import pandas as pd

import tradertools as tt

import plotly.graph_objects as go
import plotly.io as pio
from plotly.subplots import make_subplots

# Set default template with smaller margins
pio.templates.default = "plotly"
pio.templates["plotly"].layout.margin = dict(l=20, r=20, t=40, b=20) # type: ignore

PG_CLIENT = os.getenv("POSTGRES_CLIENT")

pg = tt.clients.get_pg_client(PG_CLIENT)
SCHEMA = 'hmmer_bars'


# %%
product = 'ZS'
mnths = ['F', 'H', 'K', 'N', 'Q', 'U', 'X']
start_year = 2020

row_count = 2026 - start_year
col_count = len(mnths)

# Define consistent colors for each series
colors = {
    'IQ Close': 'blue',
    'BC Close': 'red', 
    'DN Close': 'green',
    'IQP Close': 'orange'
}

subplot_titles = []
for year in range(start_year, 2026):
    for mnth in mnths:
        name2 = f"{product}{mnth}{str(year)[-2:]}"
        subplot_titles.append(name2)

# make subplot
fig = make_subplots(
    rows=row_count, cols=col_count,
    vertical_spacing=0.04,
    horizontal_spacing=0.04,
    subplot_titles=subplot_titles
)
calculated_height = row_count * 200
fig.update_layout(
    height=calculated_height,
    hovermode='x unified',
    showlegend=False
)
fig.update_xaxes(showticklabels=False)

diff_fig = make_subplots(
    rows=row_count, cols=col_count,
    vertical_spacing=0.04,
    horizontal_spacing=0.04,
    subplot_titles=subplot_titles
)
calculated_height = row_count * 200
diff_fig.update_layout(
    height=calculated_height,
    hovermode='x unified',
    showlegend=False
)
diff_fig.update_xaxes(showticklabels=False)

for ridx, year in enumerate(range(start_year, 2026)):
    for cidx, mnth in enumerate(mnths):
        r, c = ridx + 1, cidx + 1
        name2 = f"{product}{mnth}{str(year)[-2:]}"
        instdef = tt.instruments.get_definition(name2)

        #iq db data
        iq_data = pg.query(f"SELECT * FROM {SCHEMA}.\"{name2[:-3]}_30min\" WHERE name2 = '{name2}' ORDER BY dt")
        iqdf = pd.DataFrame(iq_data).set_index('dt')[['close']]

        #iq download data
        bgn_prd = iq_data[0]['dt']
        end_prd = iq_data[-1]['dt']
        download_data = tt.marketdata.historical.get_bars_in_period(name2, 30*60, bgn_prd, end_prd)
        dndf = pd.DataFrame(download_data).set_index("timestamp")[['close']]

        # barchart data
        bc_data = pg.query(f"SELECT * FROM barchart_dumps.\"{name2[:-3].lower()}_30min\" WHERE name2 = '{name2}' ORDER BY dt")
        bcdf = pd.DataFrame(bc_data).set_index('dt')[['close']]

        # iq parquet data
        iqpdf = pd.read_parquet(f"./data/{name2[:-3]}30min_dtnhistory.parquet")
        # convert string to timestamp
        # iqpdf['timestamp'] = iqpdf['timestamp'].apply(datetime.fromisoformat).apply(lambda x: x.astimezone(ZoneInfo('UTC')))
        iqpdf = iqpdf[iqpdf['exg_code'] == name2].set_index('timestamp').sort_index()
        
        # join all dfs
        df = iqdf.join(bcdf['close'], rsuffix='_bc').join(dndf['close'], rsuffix='_dn').join(iqpdf['close'], rsuffix='_iqp')
        
        # add diff columns
        df['diff_bc'] = df['close'] - df['close_bc']
        df['diff_dn'] = df['close'] - df['close_dn']
        df['diff_iqp'] = df['close'] - df['close_iqp']

        # Only show legend for first subplot (row=1, col=1)
        show_legend = (r == 1 and c == 1)

        # plot with consistent colors and single legend
        fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['close'], 
                name='IQ Close',
                line=dict(color=colors['IQ Close']),
                legendgroup='IQ Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['close_bc'], 
                name='BC Close',
                line=dict(color=colors['BC Close']),
                legendgroup='BC Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['close_dn'], 
                name='DN Close',
                line=dict(color=colors['DN Close']),
                legendgroup='DN Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['close_iqp'], 
                name='IQP Close',
                line=dict(color=colors['IQP Close']),
                legendgroup='IQP Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        
        diff_fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['diff_bc'], 
                name='IQ - BC',
                line=dict(color=colors['IQ Close']),
                legendgroup='IQ Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        diff_fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['diff_dn'], 
                name='IQ - DN',
                line=dict(color=colors['DN Close']),
                legendgroup='DN Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        diff_fig.add_trace(
            go.Scatter(
                x=df.index, 
                y=df['diff_iqp'], 
                name='IQ - IQP',
                line=dict(color=colors['IQP Close']),
                legendgroup='IQP Close',
                showlegend=show_legend
            ),
            row=r, col=c
        )
        

fig.show()
diff_fig.show()



# # join close from df to bcdf on index
# df = iqdf.join(bcdf['close'], rsuffix='_bc').join(dndf['close'], rsuffix='_dn')
# df['diff_bc'] = df['close'] - df['close_bc']
# df['diff_dn'] = df['close'] - df['close_dn']

# # First subplot - Prices
# fig1 = go.Figure()
# fig1.add_trace(go.Scatter(x=df.index, y=df['close'], name='IQ Close'))
# fig1.add_trace(go.Scatter(x=df.index, y=df['close_bc'], name='BC Close'))
# fig1.add_trace(go.Scatter(x=df.index, y=df['close_dn'], name='DN Close'))
# fig1.update_layout(title=f'Close Prices for {name2}', hovermode='x unified').show()

# # Second subplot - Differences
# fig2 = go.Figure()
# fig2.add_trace(go.Scatter(x=df.index, y=df['diff_bc'], name='IQ - BC'))
# fig2.add_trace(go.Scatter(x=df.index, y=df['diff_dn'], name='IQ - DN'))
# fig2.update_layout(title=f'Differences for {name2}', hovermode='x unified').show()

# %%
