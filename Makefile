REGISTRY = ghcr.io/luneanalytics
APPS = downloader modeler executor

.PHONY: build build-all publish publish-all $(APPS) clean

build-all: $(APPS)

publish-all: build-all
	@$(MAKE) push-all

downloader:
	@echo "Building downloader..."
	echo $(GHCR_PAT) > /tmp/ghcr_pat
	docker build --secret id=ghcr_pat,src=/tmp/ghcr_pat --no-cache \
		-f src/downloader/Dockerfile \
		-t $(REGISTRY)/hmmer_downloader:latest .
	rm -f /tmp/ghcr_pat

modeler:
	@echo "Building hmmer..."
	echo $(GHCR_PAT) > /tmp/ghcr_pat
	docker build --secret id=ghcr_pat,src=/tmp/ghcr_pat --no-cache \
		-f src/hmmer/Dockerfile \
		-t $(REGISTRY)/hmmer_modeler:latest .
	rm -f /tmp/ghcr_pat

executor:
	@echo "Building executor..."
	echo $(GHCR_PAT) > /tmp/ghcr_pat
	docker build --secret id=ghcr_pat,src=/tmp/ghcr_pat --no-cache \
		-f src/executor/Dockerfile \
		-t $(REGISTRY)/hmmer_executor:latest .
	rm -f /tmp/ghcr_pat

# Push images to registry
push-all:
	@echo "Pushing all images..."
	docker push $(REGISTRY)/hmmer_downloader:latest
	docker push $(REGISTRY)/hmmer_modeler:latest
	docker push $(REGISTRY)/hmmer_executor:latest

push-downloader:
	docker push $(REGISTRY)/hmmer_downloader:latest

push-modeler:
	docker push $(REGISTRY)/hmmer_modeler:latest

push-executor:
	docker push $(REGISTRY)/hmmer_executor:latest

# Build and publish specific app
publish:
ifdef APP
	@$(MAKE) $(APP)
	@$(MAKE) push-$(APP)
else
	@echo "Usage: make publish APP=<downloader|modeler|executor>"
	@echo "Or use: make publish-all"
endif

# Build specific app: make build APP=<app>
build:
ifdef APP
	@$(MAKE) $(APP)
else
	@echo "Usage: make build APP=<downloader|modeler|executor>"
	@echo "Or use: make build-all"
endif

clean:
	docker rmi $(REGISTRY)/hmmer_downloader:latest $(REGISTRY)/hmmer_modeler:latest $(REGISTRY)/hmmer_executor:latest 2>/dev/null || true