import logging
# from pdb import run
import numpy as np
import pandas as pd
from scipy.fft import next_fast_len
from numba import jit
import scipy.signal

log = logging.getLogger("fdiff_benchmark")

# Optimized version 1: Pre-compute coefficients and use scipy.signal
def compute_fractional_diff_coefficients(max_window, d):
    """Pre-compute fractional differencing coefficients"""
    k = np.arange(1, max_window)
    coeffs = np.concatenate(([1], np.cumprod((k - (d + 1)) / k)))
    return coeffs

def rolling_fractional_differencing_v1(group: pd.DataFrame, column, window, d):
    """Optimized version using pre-computed coefficients"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute coefficients once
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # Extract window data and center it
        window_data = data[i - window + 1:i + 1]
        centered_data = window_data - np.mean(window_data)
        
        # Apply fractional differencing using convolution
        # Reverse coefficients for proper convolution
        diff_result = np.convolve(centered_data, coeffs[:window], mode='valid')
        result[i] = diff_result[-1] if len(diff_result) > 0 else centered_data[-1]
    
    return pd.Series(result, index=group.index)

# Optimized version 2: Numba JIT compiled version
@jit(nopython=True)
def fractional_diff_numba(data, coeffs, window):
    """Numba-compiled fractional differencing for a single window"""
    n = len(data)
    centered = data - np.mean(data)
    result = 0.0
    
    for j in range(min(window, len(coeffs))):
        if j < n:
            result += coeffs[j] * centered[n - 1 - j]
    
    return result

def rolling_fractional_differencing_v2(group: pd.DataFrame, column, window, d):
    """Numba-optimized version"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute coefficients
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_data = data[i - window + 1:i + 1]
        result[i] = fractional_diff_numba(window_data, coeffs, window)
    
    return pd.Series(result, index=group.index)

# Optimized version 3: Vectorized approach with scipy.signal
def rolling_fractional_differencing_v3(group: pd.DataFrame, column, window, d):
    """Vectorized approach using scipy.signal.lfilter"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute coefficients
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result = np.full(n, np.nan)
    
    # Use scipy's lfilter for efficiency
    for i in range(window - 1, n):
        window_data = data[i - window + 1:i + 1]
        centered_data = window_data - np.mean(window_data)
        
        # Apply filter (note: lfilter applies coefficients in forward direction)
        filtered = scipy.signal.lfilter(coeffs[:window], [1], centered_data)
        result[i] = filtered[-1]
    
    return pd.Series(result, index=group.index)

# Most optimized version 4: Minimize redundant calculations
@jit(nopython=True)
def rolling_fractional_diff_core(data, coeffs, window):
    """Core computation with minimal overhead"""
    n = len(data)
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # Extract window
        start_idx = i - window + 1
        # window_sum = 0.0
        
        # Compute mean
        mean_val = 0.0
        for j in range(window):
            mean_val += data[start_idx + j]
        mean_val /= window
        
        result[i] = 0.0
        
        # Apply fractional differencing
        for j in range(window):
            if j < len(coeffs):
                result[i] += coeffs[j] * (data[start_idx + window - 1 - j] - mean_val)
        
    return result

def rolling_fractional_differencing_v4(group: pd.DataFrame, column, window, d):
    """Most optimized version with numba"""
    group = group.sort_index()
    data = group[column].values
    
    # Pre-compute coefficients
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result_array = rolling_fractional_diff_core(data, coeffs, window)
    
    return pd.Series(result_array, index=group.index)

# Alternative: FFT-based approach with better memory management
def rolling_fractional_differencing_fft_optimized(group: pd.DataFrame, column, window, d):
    """FFT-optimized version that reuses computations"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute FFT size and coefficients
    np2 = next_fast_len(window + window - 1)
    k = np.arange(1, window)
    b_coeffs = np.concatenate(([1], np.cumprod((k - (d + 1)) / k)))
    
    # Pad coefficients for FFT
    b_padded = np.concatenate((b_coeffs, np.zeros(np2 - window)))
    b_fft = np.fft.fft(b_padded)  # Pre-compute FFT of coefficients
    
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_data = data[i - window + 1:i + 1]
        centered_data = window_data - np.mean(window_data)
        
        # Pad data for FFT
        data_padded = np.concatenate((centered_data, np.zeros(np2 - window)))
        data_fft = np.fft.fft(data_padded)
        
        # Convolution via FFT
        conv_result = np.fft.ifft(b_fft * data_fft)[:window]
        result[i] = np.real(conv_result[-1])
    
    return pd.Series(result, index=group.index)

# Original functions for comparison
def diffseries_original(x, d):
    """Original diffseries function"""
    iT = len(x)
    if iT < 2:
        raise ValueError("Length of x must be at least 2")
    x = x - np.mean(x)
    np2 = next_fast_len(iT + iT - 1)
    pad = np.zeros(np2 - iT) # type: ignore
    k = np.arange(1, iT)
    b = np.concatenate(([1], np.cumprod((k - (d + 1)) / k), pad))
    dx = np.fft.ifft(np.fft.fft(b) * np.fft.fft(np.concatenate((x, pad))))[:iT]
    return np.real(dx)

def rolling_fractional_differencing_original(group: pd.DataFrame, column, window, d):
    """Original rolling fractional differencing function"""
    group = group.sort_index()
    result = np.full(group.shape[0], np.nan)
    for i in range(len(group), window - 1, -1):
        result[i - 1] = diffseries_original(group[column].iloc[i - window:i].values, d)[-1]
    return pd.Series(result, index=group.index)

# Accuracy comparison function
def compare_results(original_result, test_result, method_name, rtol=1e-10, atol=1e-12):
    """Compare two results and return accuracy metrics"""
    if original_result.shape != test_result.shape:
        return {
            'accurate': False,
            'max_error': np.inf,
            'mean_error': np.inf,
            'error_message': f"Shape mismatch: {original_result.shape} vs {test_result.shape}"
        }
    
    # Handle NaN values - they should be in the same positions
    orig_nan_mask = np.isnan(original_result)
    test_nan_mask = np.isnan(test_result)
    
    if not np.array_equal(orig_nan_mask, test_nan_mask):
        return {
            'accurate': False,
            'max_error': np.inf,
            'mean_error': np.inf,
            'error_message': "NaN patterns don't match"
        }
    
    # Compare non-NaN values
    valid_mask = ~orig_nan_mask
    if not np.any(valid_mask):
        return {
            'accurate': True,
            'max_error': 0.0,
            'mean_error': 0.0,
            'error_message': "All values are NaN"
        }
    
    orig_valid = original_result[valid_mask]
    test_valid = test_result[valid_mask]
    
    # Calculate errors
    abs_error = np.abs(orig_valid - test_valid)
    rel_error = np.abs((orig_valid - test_valid) / (orig_valid + 1e-15))  # Add small epsilon to avoid division by zero
    
    max_abs_error = np.max(abs_error)
    mean_abs_error = np.mean(abs_error)
    max_rel_error = np.max(rel_error)
    
    # Check if results are close enough
    is_accurate = np.allclose(orig_valid, test_valid, rtol=rtol, atol=atol)
    
    return {
        'accurate': is_accurate,
        'max_abs_error': max_abs_error,
        'mean_abs_error': mean_abs_error,
        'max_rel_error': max_rel_error,
        'error_message': None if is_accurate else f"Max abs error: {max_abs_error:.2e}, Max rel error: {max_rel_error:.2e}"
    }

# Enhanced benchmark function with accuracy comparison and formatted output
def benchmark_and_compare(group, column, window, d, num_runs=3, rtol=1e-10, atol=1e-12):
    """
    Comprehensive benchmark with accuracy comparison and formatted results
    
    Parameters:
    -----------
    group : pd.DataFrame
        Input data group
    column : str
        Column name to process
    window : int
        Rolling window size
    d : float
        Fractional differencing parameter
    num_runs : int
        Number of timing runs for averaging
    rtol, atol : float
        Relative and absolute tolerance for accuracy comparison
    """
    import time
    
    methods = [
        ("Original", rolling_fractional_differencing_original),
        ("V1 - Precomputed coeffs", rolling_fractional_differencing_v1),
        ("V2 - Numba", rolling_fractional_differencing_v2),
        ("V3 - Scipy signal", rolling_fractional_differencing_v3),
        ("V4 - Numba optimized", rolling_fractional_differencing_v4),
        ("FFT optimized", rolling_fractional_differencing_fft_optimized)
    ]
    
    results = {}
    original_result = None
    
    log.debug("Benchmarking fractional differencing methods...")
    log.debug(f"Dataset shape: {group.shape}, Window: {window}, d: {d}")
    log.debug(f"Number of timing runs: {num_runs}\n")
    
    for method_name, method_func in methods:
        log.debug(f"Testing {method_name}...")
        
        try:
            # First run to get the result and warm up JIT if applicable
            result = method_func(group, column, window, d)
            
            # Store original result for comparison
            if method_name == "Original":
                original_result = result.values.copy()
                original_time = []
            
            # Time multiple runs
            times = []
            for run in range(num_runs):
                start_time = time.perf_counter()
                _ = method_func(group, column, window, d)
                end_time = time.perf_counter()
                times.append(end_time - start_time)
                
                if method_name == "Original":
                    original_time.append(end_time - start_time) # type: ignore
            
            avg_time = np.mean(times)
            std_time = np.std(times)
            
            # Compare accuracy with original
            if method_name == "Original":
                accuracy_info = {
                    'accurate': True,
                    'max_abs_error': 0.0,
                    'mean_abs_error': 0.0,
                    'max_rel_error': 0.0,
                    'error_message': "Reference method"
                }
            else:
                accuracy_info = compare_results(
                    original_result, result.values, method_name, rtol, atol
                )
            
            results[method_name] = {
                'avg_time': avg_time,
                'std_time': std_time,
                'min_time': min(times),
                'result_shape': result.shape,
                'success': True,
                'accuracy': accuracy_info
            }
            
        except Exception as e:
            results[method_name] = {
                'success': False,
                'error': str(e),
                'accuracy': {'accurate': False, 'error_message': f"Method failed: {str(e)}"}
            }
            log.debug(f"  ❌ Failed: {e}")
    
    # Calculate speedups relative to original
    if "Original" in results and results["Original"]['success']:
        original_avg_time = results["Original"]['avg_time']
        for method_name in results:
            if results[method_name]['success'] and method_name != "Original":
                speedup = original_avg_time / results[method_name]['avg_time']
                results[method_name]['speedup'] = speedup
            elif method_name == "Original":
                results[method_name]['speedup'] = 1.0
    
    # Print formatted results table
    print_benchmark_table(results)
    
    return results

def print_benchmark_table(results):
    """Print a nicely formatted benchmark results table"""
    
    # Table headers
    headers = ["Method", "Time (s)", "Speedup", "Accuracy", "Status"]
    
    # Calculate column widths
    col_widths = [len(h) for h in headers]
    
    # Prepare table data
    table_data = []
    for method_name, result in results.items():
        if result['success']:
            # Format time with uncertainty
            time_str = f"{result['avg_time']:.4f}±{result['std_time']:.4f}"
            
            # Format speedup
            if 'speedup' in result:
                if result['speedup'] >= 10:
                    speedup_str = f"{result['speedup']:.1f}x"
                else:
                    speedup_str = f"{result['speedup']:.2f}x"
            else:
                speedup_str = "1.00x"
            
            # Format accuracy
            acc = result['accuracy']
            if acc.get('accurate', False):
                if method_name == "Original":
                    accuracy_str = "Reference"
                else:
                    max_err = acc.get('max_abs_error', 0.0)
                    accuracy_str = f"✓ (err<{max_err:.1e})"
            else:
                accuracy_str = f"✗ {acc.get('error_message', 'Failed')}"
            
            status_str = "✓ Success"
            
        else:
            time_str = "N/A"
            speedup_str = "N/A"
            accuracy_str = "✗ Failed"
            status_str = f"✗ {result.get('error', 'Unknown error')}"
        
        row = [method_name, time_str, speedup_str, accuracy_str, status_str]
        table_data.append(row)
        
        # Update column widths
        for i, cell in enumerate(row):
            col_widths[i] = max(col_widths[i], len(str(cell)))
    
    # Print table
    print("\n" + "="*80)
    print("BENCHMARK RESULTS")
    print("="*80)
    
    # Print headers
    header_row = " | ".join(h.ljust(col_widths[i]) for i, h in enumerate(headers))
    print(header_row)
    print("-" * len(header_row))
    
    # Print data rows
    for row in table_data:
        data_row = " | ".join(str(cell).ljust(col_widths[i]) for i, cell in enumerate(row))
        print(data_row)
    
    print("="*80)
    
    # Print summary
    successful_methods = [name for name, result in results.items() if result['success']]
    if len(successful_methods) > 1:
        fastest_method = min(
            [name for name in successful_methods if name != "Original"],
            key=lambda x: results[x]['avg_time'],
            default=None
        )
        if fastest_method and 'speedup' in results[fastest_method]:
            print(f"\n🏆 Fastest method: {fastest_method} ({results[fastest_method]['speedup']:.1f}x speedup)")
    
    # Print accuracy summary
    accurate_methods = [
        name for name, result in results.items() 
        if result['success'] and result['accuracy']['accurate']
    ]
    if len(accurate_methods) < len(successful_methods):
        print(f"⚠️  Warning: {len(successful_methods) - len(accurate_methods)} methods have accuracy issues")
    
    print("\n")

# Example usage function
def run_example_benchmark():
    """Run an example benchmark with sample data"""
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range('2010-01-01', periods=1000, freq='D')
    sample_data = pd.DataFrame({
        'date': dates,
        'price': 100 + np.cumsum(np.random.randn(1000) * 0.1)
    }).set_index('date')
    
    # Run benchmark
    results = benchmark_and_compare(
        group=sample_data,
        column='price',
        window=50,
        d=0.5,
        num_runs=3
    )
    
    return results



run_example_benchmark()