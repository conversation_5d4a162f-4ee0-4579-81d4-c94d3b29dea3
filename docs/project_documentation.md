# Intraday Models - Stochastic Markov Process Project Documentation

## Project Overview
This project implements Hidden Markov Models (HMM) for market regime detection and trading strategy development across various futures markets. It features regime classification, backtesting capabilities, and parameter optimization through grid searching.

The first script to look at that will be easiest to follow is st_mprocess_slowloop.py This is a slow loop so you can see what is happening each train/test step of the model.

In production, the actual model training will happen for each product each time a new expiry becomes the front month, all previous 'front month' contracts will be used in training for that product
then predictions will be made from the model for that product once every 4 hours, but the model will not be retrained until the next front month roll. It's basically just following the logic of the backtest.
## File Structure

### Core Python Scripts

#### `ohlc_utils.py`
**Purpose**: Utilities for processing OHLC (Open-High-Low-Close) market data.
**Key Functions**:
- `calculate_expiry_dates`: Determines expiration dates for futures contracts
- `add_expiry_position`: Adds expiry position metrics to market data
- `create_continuous_futures`: Constructs continuous futures series from discrete contracts
**Dependencies**: pandas, numpy

#### `st_mprocess_models.py`
**Purpose**: Contains the core Hidden Markov Model implementations
**Key Functions**:
- `add_hmm_regime_fc_fwd`: Main HMM implementation for forward-looking regime detection
- `smooth_states_weighted`: Smoothing algorithm for regime state transitions
**Dependencies**: numpy, pandas, scikit-learn (for HMM)

#### `st_mprocess_helper.py`
**Purpose**: Helper functions for data processing and performance analysis
**Key Functions**:
- `diffseries`: Creates differenced time series
- `rolling_fractional_differencing`: Implements fractional differentiation
- `parallel_rolling_fd`: Parallel implementation of rolling fractional differencing
- `calculate_close_from_extrema`: Calculates closing prices relative to high/low extrema
- `summarize_model_performance_sltp`: Comprehensive backtesting performance analysis
**Dependencies**: numpy, pandas, multiprocessing

#### `st_mprocess_parallel.py`
**Purpose**: Main Markov process implementation with parallelization capabilities
**Dependencies**: st_mprocess_models, st_mprocess_helper, ohlc_utils

#### `st_mprocess_gridparallel.py`
**Purpose**: Parallel implementation of grid search for parameter optimization
**Dependencies**: multiprocessing, st_mprocess_parallel

#### `st_mprocess_slowloop.py`
**Purpose**: Implementations of slower, more computationally intensive backtest processes. Easier to read/understand.
Gives identical output to parallel version about 8-16x slower :)
**Dependencies**: st_mprocess_helper

#### `st_mprocess_modelparams.py`
**Purpose**: Parameter definitions and configurations for the Markov models
**Dependencies**: None direct; used by other model files

#### `results_compile.py`
**Purpose**: Aggregates, processes, and visualizes model results across different markets
**Dependencies**: pandas, numpy, matplotlib/plotly for visualization, all model output files

### Data Files

#### Model Performance Files (`smrybars_*.csv`)
**Purpose**: Summary of model performance using the optimal parameter configurations
**Contents**: Datetime, price data (Open, High, Low, Close), volume, derived metrics, regime information, and performance metrics
**Markets Covered**: KC (Coffee), SB (Sugar), ZC (Corn), NQ (Nasdaq), ZW (Wheat), HG (Copper), ZS (Soybean), CL (Crude Oil), GC (Gold), NG (Natural Gas)

#### Grid Search Results (`grid_search_summaries_*.csv`) 
**Purpose**: Comprehensive results from grid search parameter optimization
**Contents**: Win percentages, trade statistics, risk metrics, Sharpe ratios, and corresponding parameter settings used to determine the optimal model configuration
**Markets Analyzed**: Various futures markets matching the smrybars data files

### Documentation
#### `model_description.md`
**Purpose**: Technical documentation for the HMM regime classification function
**Contents**: Detailed explanation of the add_hmm_regime_fc_fwd function, parameters, and methodology

#### `project_documentation.md`
**Purpose**: This file - comprehensive overview of project structure and file dependencies
**Contents**: Summaries of all project files and their relationships

## Data Sources
The project ingests raw futures market data from a SharePoint directory:
{user_dir}\OneDrive - JA Technologies IV ApS\LuneFiles\FuturesData\barchart\futs_parquets{ibarsize}min
Where:
- `user_dir` refers to the user's home directory
- `ibarsize` is the bar timeframe in minutes (30 minutes for the current implementation)

The raw data is in Parquet format, which is processed through the various scripts to generate the model outputs and performance summaries.

## Dependencies

Main libraries: 
- pandas, numpy: Core data processing
- scikit-learn: For HMM implementation
- matplotlib/plotly: For visualization
- multiprocessing: For parallelization

Internal dependencies flow:
1. ohlc_utils.py ← Used by most scripts for data preprocessing
2. st_mprocess_models.py and st_mprocess_helper.py ← Core functionality used by other scripts
3. st_mprocess_parallel.py ← Main orchestration used by grid implementation
4. Results processing scripts depend on outputs from modeling scripts

