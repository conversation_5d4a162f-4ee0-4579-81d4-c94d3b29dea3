# Hidden Markov Model for Regime Classification (`add_hmm_regime_fc_fwd`)

The `add_hmm_regime_fc_fwd` function utilizes a Gaussian Hidden Markov Model (HMM) for market regime classification, ensuring consistency and preventing look-ahead bias.

## Overview

The Hidden Markov Model infers market regimes from historical data and predicts future regimes in real-time-like conditions. It employs smoothing and dynamic transitions to enhance stability.

## Parameters

- `df` *(DataFrame)*: Input data containing the required time series and features.
- `uexpiry`: The expiry period under analysis.
- `feature_columns` *(list, optional)*: Columns for model features
- `smoothing_window` *(int)*: Size of the window for smoothing regime predictions.
- `smoothing_alpha` *(float)*: Decay factor for smoothing regimes.
- `covariance_type` *(str)*: Covariance assumption ("diag", "full", "tied").
- `mthsback` *(int)*: Duration (in months) of historical data for model training.
- `fwdper` *(int)*: Forward period parameter defines relative futures period to predict for, eg 1 = front momnth
- `sticky_factor` *(float)*: Probability boosting factor for preserving existing regime.
- `trained_model`, `scaler`, `regime_mapping`: Continued and reusable model components.
- `window_size` *(int)*: Number of historical points available for forward predictions.
- `forward_bars` *(int)*: Count of forward-looking bars return for the prediction.

## Model Training Steps

1. **Historical Data Selection**: Identifies a suitable historical period for initial training without future data.
2. **Data Preprocessing**: Computes forward-looking returns, handles missing data, and standardizes features.
3. **Model Fitting**: Trains a Gaussian HMM with specified covariance and state components (`n_components=3`).
4. **State Prediction**: Iteratively generates hidden states for historical data ensuring prediction stability.

## Dynamic Transition Matrix

- Dynamically calculates transition probabilities from past state sequences.
- Applies a "sticky" parameter increasing the likelihood of state continuity to achieve stable regimes.

## Dynamic Transition Matrix Explained

The dynamic transition matrix represents probabilities of shifting from one hidden regime (state) to another within the Hidden Markov Model. In standard HMM applications, these probabilities often stay static. However, in financial modeling contexts, market behavior can shift, necessitating adaptive probabilities.

### Components of Dynamic Transitions

- **Empirical Calculation**: Transition probabilities are directly derived from historical state occurrences. By counting each instance of a transition between states during training, the model constructs realistic likelihoods of moving from one state to another.

- **Sticky Adjustment (Persistence Factor)**: A "sticky factor" (`sticky_factor`) enhances the probability of remaining in the current state. Multiplying diagonal (self-transition) probabilities by this factor increases model inertia, reducing erratic switching between states, and emulating realistic market inertia or persistence in regimes.

#### Example

Consider a simplified scenario with 3 states (bullish, neutral, bearish):

| From State   | To Bullish | To Neutral | To Bearish |
|--------------|------------|------------|------------|
| **Bullish**  | 0.70       | 0.15       | 0.15       |
| **Neutral**  | 0.20       | 0.60       | 0.20       |
| **Bearish**  | 0.10       | 0.10       | 0.80       |

- Higher diagonal values indicate a strong persistence factor (sticky factor), meaning once the market is in a particular regime, it is more likely to remain there.
- Off-diagonal values quantify the probability of transitioning between regimes, allowing responsive yet controlled regime switching.

This dynamic approach ensures prediction stability, reduces noisy regime transitions, and mirrors realistic market regime persistence, thus providing robust and interpretable results.

## Forward Prediction (Real-time Emulation)

- Predicts regimes on sequential data, ensuring forward-looking logic to prevent data leakage.
- Standardizes new observations with historical scaling.
- Operates within the predetermined `window_size`, simulating real-time constraints.

## Smoothing

- Applies smoothing parameters (`smoothing_window` and `smoothing_alpha`) to stabilize state fluctuations and reduce short-lived regime shifts.

## Outputs

The function returns:

- **`updated_df`**: Dataframe including predicted and smoothed regime labels.
- **`trained_model`**: Gaussian HMM trained model (persistently usable).
- **`scaler`**: Feature scaling object for standardized input normalization.
- **`regime_mapping`**: Mapping from numerical regime states to specific market condition interpretations.

---

The model reliably classifies regimes, allowing quantitative strategies to adapt dynamically without bias
