<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.0.3">
  <diagram name="Page-1" id="T2qT1bCaCQlTz1pYLBvM">
    <mxGraphModel grid="1" page="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" pageScale="1" pageWidth="850" pageHeight="1400" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-1" value="Check if retraining is necessary (A new expiry is becoming the front month)" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="200" y="190" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-2" value="" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.annotation_2;align=left;labelPosition=right;pointerEvents=1;rotation=90;" vertex="1" parent="1">
          <mxGeometry x="225" y="70" width="50" height="501.25" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="t_5xOFZ1yljX-cBeAQrN-3" target="jqgW21zvxQKMtDxm0GwW-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="t_5xOFZ1yljX-cBeAQrN-3">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-3" value="Yes" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="-40" y="350" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="t_5xOFZ1yljX-cBeAQrN-4" target="jqgW21zvxQKMtDxm0GwW-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="t_5xOFZ1yljX-cBeAQrN-4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="610" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-4" value="No" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="460" y="350" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="t_5xOFZ1yljX-cBeAQrN-6" target="t_5xOFZ1yljX-cBeAQrN-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-6" value="Extract 5 years of 30min bars for all contract months." style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="-195" y="39.06" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-7" value="Run fracdiff function for both fd value/windows by expiry month, (grouping col = expiry)" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="-70" y="30.62" width="120" height="120" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="t_5xOFZ1yljX-cBeAQrN-9" target="t_5xOFZ1yljX-cBeAQrN-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-9" value="Identify and filter front month expiry to get continuous time series of the rolling front month. (fwdper = 1)" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="460" y="39.06000000000001" width="101.87" height="101.87" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-16" value="Calculate forward n return based on forward_bars" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-86.25" y="540" width="172.5" height="80" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-19" value="Add 1 period (4H) return based on Close by expiry" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="330" y="40.00000000000001" width="101.25" height="101.25" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-21" value="Use resample_by_&lt;div&gt;group fn to resample data to 4H by expiry&lt;/div&gt;" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="69.99999999999999" y="39.06" width="101.87" height="101.87" as="geometry" />
        </mxCell>
        <mxCell id="t_5xOFZ1yljX-cBeAQrN-36" value="run Calculate Close from extrema function on 4H data." style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="200" y="39.059999999999995" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-4" value="Filter date to mthsback start date (create training set that is that many months back)" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-82.5" y="450" width="165" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-6">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="120" y="850" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-6" value="Fit new Gaussian HMM, update with dynamic transition matrix" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-86.25" y="810" width="172.5" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-7" value="Isolate feature variables to create training set" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-86.25" y="630" width="172.5" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-8">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="120" y="760" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-8" value="Establish scalar to be used in training. Save for application to all future data for this expiry." style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-86.25" y="720" width="172.5" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-9" value="Save Scalar" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="130" y="720" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-10" target="jqgW21zvxQKMtDxm0GwW-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-10" value="Save Model" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="130" y="810" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-11" value="Isolate feature variables up-to most recent" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="415.63" y="450" width="168.75" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-12" value="Apply scalar from most recent training" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="415.62" y="530" width="168.75" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=-0.002;entryY=0.392;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-9" target="jqgW21zvxQKMtDxm0GwW-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-16" value="Apply model from most recent training to predict states in window" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="415.62" y="690" width="168.75" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-17" value="filter data to 50 observation window" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="415.62" y="610" width="168.75" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-18" value="Apply smooth_states_weighted function and get most recent state (Trending down, flat, Trending up)&lt;div&gt;[-1,0,1]&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="407.81" y="770" width="184.37" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-21" target="jqgW21zvxQKMtDxm0GwW-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-21" target="jqgW21zvxQKMtDxm0GwW-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-21" target="jqgW21zvxQKMtDxm0GwW-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-21" value="Process Execution Rule:" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="407.81" y="940" width="182.19" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-22" value="Close any open position for prior expiry month" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-24" value="Start new empty state series for this expiry month" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-95" y="910" width="190" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-25" target="jqgW21zvxQKMtDxm0GwW-32">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="670" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-25" value="Get most recent state predicted and save to state series" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="407.81" y="860" width="182.19" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-26" value="-1" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="310" y="1060" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-27" target="jqgW21zvxQKMtDxm0GwW-30">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-27" value="0" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="458.9" y="1060" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-28" value="1" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="620.0000000000001" y="1060" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-29" value="last -1: stay short (do nothing)&lt;div&gt;last 0: enter short&lt;/div&gt;&lt;div&gt;last 1: reverse long to short&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="240" y="1180" width="164.37" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-30" value="last -1: go flat&lt;div&gt;last 0: stay flat (do nothing)&lt;/div&gt;&lt;div&gt;last 1: go flat&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="418.90000000000003" y="1180" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-31" value="last -1: reverse short to long&lt;div&gt;last 0: enter long&lt;/div&gt;&lt;div&gt;last 1: stay long (do nothing)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="1180" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-32" value="Save state to state series" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="630" y="850" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-33" target="jqgW21zvxQKMtDxm0GwW-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-33" value="Get existing state series for this expiry month" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="630" y="350" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-42" value="Do this every 4H for each product included in the trade" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;" vertex="1" parent="1">
          <mxGeometry x="-300" width="90" height="170" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.66;entryY=-0.037;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-26" target="jqgW21zvxQKMtDxm0GwW-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.371;entryY=-0.02;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-28" target="jqgW21zvxQKMtDxm0GwW-31">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-59" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-56" target="t_5xOFZ1yljX-cBeAQrN-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-56" value="&lt;b&gt;Start Here&lt;/b&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
          <mxGeometry x="-206.25" width="126.25" height="30.62" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-57">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="720" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-57" value="If this is first prediction of a new expiry month, state series will be empty and this prediction will create the first state. Note that each expiry month will have it&#39;s own state series that gets filled though time so long as it&#39;s the front month until it rolls." style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;" vertex="1" parent="1">
          <mxGeometry x="750" y="227.5" width="90" height="325" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-64" value="fd1_window, fd1_d&lt;div&gt;fd2_window, fd2_d&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-100" y="141.25" width="180" height="90" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-66" value="norm_position_window" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="-30" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-68" value="mthsback" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-196.25" y="490" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-69" value="forward_bars" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-190" y="575" width="120" height="55" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-70" value="price_fd1&lt;div&gt;price_fd2&lt;/div&gt;&lt;div&gt;norm_position&lt;br&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-206.25" y="650" width="140" height="85" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-71" value="sticky_factor&lt;div&gt;covariance_type&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-223.12" y="840" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-72" value="window_size" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="640" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="jqgW21zvxQKMtDxm0GwW-73">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="600" y="805" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-73" value="smoothing_alpha&lt;div&gt;smoothing_window&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="765" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-74" value="Set Stop Loss/Adjusted Stop Trigger" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="373.90000000000003" y="1290" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-75" value="stop&lt;div&gt;stopadj&lt;/div&gt;&lt;div&gt;trail&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="1290" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="jqgW21zvxQKMtDxm0GwW-76" value="This shape is a parameter" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-311.56" y="170" width="113.12" height="100" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
