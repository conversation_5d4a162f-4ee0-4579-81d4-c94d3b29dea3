__all__ = ["Timeframe"]

from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

class Timeframe:
    def __init__(self, name: str):
        self.name = name
        
        if 'min' in name:
            self.interval = int(name.split('min')[0])
            self.unit = 'min'
        elif 'h' in name:
            self.interval = int(name.split('h')[0])
            self.unit = 'h'
        else:
            raise ValueError(f"Unsupported timeframe: {name}")  
        
    
    def current_bar(self, tz: str = "America/New_York") -> datetime:
        """Returns the current bar time for this timeframe."""
        cbt = datetime.now(ZoneInfo(tz))
        
        minutes = cbt.minute - (cbt.minute % self.interval)
        cbt = cbt.replace(minute=minutes, second=0, microsecond=0)
        return cbt
        
    
    def latest_full_bar(self) -> datetime:
        """Returns the latest bar time for this timeframe."""
        return self.current_bar() - timedelta(minutes=self.interval)

    
    @property
    def td_interval(self) -> timedelta:
        if self.unit == 'min':
            return timedelta(minutes=self.interval)
        elif self.unit == 'h':
            return timedelta(hours=self.interval)
        else:
            raise ValueError(f"Unsupported timeframe: {self.name}")