import os
import yaml
import logging
import time as ttime
from typing import Union
from datetime import datetime, time, timedelta
from zoneinfo import ZoneInfo

import tradertools as tt
from tradertools.marketdata.historical import Bar

from .timeframe import Timeframe

log = logging.getLogger(__name__)

PG_CLIENT = os.getenv("POSTGRES_CLIENT")

if PG_CLIENT is None:
    raise ValueError("POSTGRES_CLIENT environment variable not set")

SCHEMA = 'hmmer_bars'

INCLUDE_EXPIRED = False
EARLIEST_YEAR = 2005
TIMEOUT_RETRIES = 3
YEARS_OF_DATA = 3






def main():
    """Entry point for the hmmer_downloader."""
    pg = tt.clients.get_pg_client(PG_CLIENT)
    
    with open("./config/config.yaml", "r") as f:
        data = yaml.safe_load(f)
        products = data["products"] 
        timeframes = [Timeframe(tf) for tf in data["timeframes"]]
        
    # products = ['CL']
    
    log.info(products)
    
    all_definitions = tt.instruments.get_definitions(include_expired=INCLUDE_EXPIRED)
    all_definitions = sorted(all_definitions, key=lambda x: x.expiry if x.expiry is not None else datetime.min.date())
    
    for p in products:
        
        # get all definitions for this product
        definitions = [d for d in all_definitions if d.product == p and d.expiry is not None and d.expiry.year >= EARLIEST_YEAR] 
        log.info(f"Found {len(definitions)} definitions for {p}")
        
        # only include instruments for expiries within the last YEARS_OF_DATA years
        definitions = [d for d in definitions if d and d.expiry and d.expiry <= (datetime.now() + timedelta(days=365*YEARS_OF_DATA)).date()]

        log.info(f"definitions to update: {[d.name2 for d in definitions]}")
        
        for tf in timeframes:
            
            # check if table exists
            table_name = f"{p}_{tf.name}"
            if not pg.table_exists(SCHEMA, table_name):
                log.info(f"Table {table_name} does not exist. Creating...")
                create_table(pg, SCHEMA, table_name)
            
            for d in definitions:
                if d.expiry is None or d.name2 is None or d.contractMonth is None:
                    log.debug(f"Skipping {d}. Missing expiry, name2, or contract month.")
                    continue
                
                expiry_end = datetime.combine(d.expiry, time(17, 0, 0, tzinfo=ZoneInfo("America/New_York"))).astimezone(ZoneInfo("UTC"))
                
                # if expiry is not correct for synthetic contracts try to grab an extra 30 days just to be sure to have everything 
                expiry_end = expiry_end + timedelta(days=30)
                
                end_prd = min(expiry_end, tf.latest_full_bar())
                
                bgn_prd = end_prd - timedelta(days=365*YEARS_OF_DATA)
                last_data_time = query_max_time(pg, SCHEMA, table_name, d.name2)
                if last_data_time is not None:
                    bgn_prd = max(bgn_prd, last_data_time.astimezone(ZoneInfo("UTC")) + tf.td_interval)
                
                if bgn_prd > end_prd:
                    log.warning(f"begin_prd {bgn_prd} is after end_prd {end_prd}. Skipping {d.name2}")
                    continue
                
                
                log.info(f"Downloading {d.name2} from {bgn_prd} to {end_prd}")
                data = []
                retries = 0
                while retries < TIMEOUT_RETRIES:
                    try:
                        data = tt.marketdata.historical.get_bars_in_period(d.name2, tf.interval*60, bgn_prd, end_prd, timeout=5)
                        log.debug(f"Downloaded {len(data)} bars for {d.name2}")
                        break
                    except TimeoutError:
                        if retries < TIMEOUT_RETRIES :
                            log.warning(f"Timeout downloading bars for {d.name2}. retrying...")
                            ttime.sleep(5)
                        retries += 1
                        continue
                    except Exception as e:
                        log.error(f"Error downloading bars for {d.name2}: {e}")
                        break
                if retries == TIMEOUT_RETRIES:
                    log.error(f"Failed to download bars for {d.name2} after {retries} retries.")
                    continue

                if not data:
                    log.info(f"Skipping {d.name2}. No data downloaded.")
                    continue
                expiry = int(d.expiry.strftime('%Y%m'))
                rows = [bar_to_db_row(bar, expiry) for bar in data]
                log.debug(f"Inserting {len(rows)} rows for {d.name2}")
                try:
                    pg.insert_bulk(f"{SCHEMA}.\"{table_name}\"", rows, chunk_size=10000)
                    log.info(f"Inserted {len(rows)} rows for {d.name2}")
                except Exception as e:
                    log.error(f"Error inserting rows for {d.name2}: {e}")
                    



def create_table(client, schema, table_name):
    query = f"""
    CREATE TABLE IF NOT EXISTS {schema}."{table_name}" (
        dt TIMESTAMP WITH TIME ZONE,
        name2 VARCHAR(32),
        expiry INTEGER,
        open FLOAT,
        high FLOAT,
        low FLOAT,
        close FLOAT,
        volume INTEGER,
        CONSTRAINT {table_name}_dt_name2_unique UNIQUE (dt, name2)
    );
    CREATE INDEX IF NOT EXISTS idx_CL_30min_name2_dt ON hmmer_bars."CL_30min" (name2, dt);
    CREATE INDEX IF NOT EXISTS idx_CL_30min_expiry_dt ON hmmer_bars."CL_30min" (expiry, dt);
    """
    client.execute(query)



def query_max_time(client, schema, table_name, name2: str) -> Union[datetime, None]:
    query = f"SELECT MAX(dt) FROM {schema}.\"{table_name}\" WHERE name2 = '{name2}'"
    res = client.query(query)
    if len(res) == 0:
        return None
    elif res[0]['max'] is None:
        return None
    else: 
        return res[0]['max']
        
    
    
def bar_to_db_row(bar: Bar, expiry: int):
    log.debug(f"bar_to_db_row: bar - {bar}, expiry: {expiry}")
    return {
        'dt': bar.timestamp,
        'name2': bar.name2,
        'expiry': expiry,
        'open': bar.open,
        'high': bar.high,
        'low': bar.low,
        'close': bar.close,
        'volume': bar.volume
    }
