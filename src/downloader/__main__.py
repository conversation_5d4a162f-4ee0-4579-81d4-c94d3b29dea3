import os
import sys
import logging
import logging.handlers
from pathlib import Path

import setproctitle
from dotenv import load_dotenv
load_dotenv()

# Get app title from environment or use default
TITLE = "hmmer_downloader"

setproctitle.setproctitle(TITLE)

logging.getLogger('tradertools').setLevel(logging.INFO)
logging.getLogger('asyncio').setLevel(logging.WARNING)

# Create logs directory if it doesn't exist
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

# Create rotating file handler that rotates at midnight
file_handler = logging.handlers.TimedRotatingFileHandler(
    filename=logs_dir / f"{TITLE}.log",
    when='midnight',
    interval=1,
    backupCount=30,  # Keep 30 days of logs
    encoding='utf-8'
)
# Set the suffix for rotated files to just the date
file_handler.suffix = "_%Y%m%d"
file_handler.namer = lambda name: name.replace(".log._", "_") + ".log"

LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

logging.basicConfig(
    level=getattr(logging, LOG_LEVEL, logging.INFO),
    format='%(asctime)s.%(msecs)03d | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout),
        file_handler,
    ]
)
log = logging.getLogger(__name__)


from downloader.main import main

if __name__ == "__main__":
    main()
