# from scipy.fftpack import next_fast_len
import time
import logging

import numpy as np
import pandas as pd
from scipy.fft import next_fast_len
from joblib import <PERSON>llel, delayed

from hmmer.fdff_funcs import funcs

# import hmmer.mtest_fdiff_calc_versions as benchmark

log = logging.getLogger(__name__)

pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 500)
pd.set_option('display.width', 1000)

def diffseries(x, d):
    iT = len(x)
    if iT < 2:
        raise ValueError("Length of x must be at least 2")

    x = x - np.mean(x)
    np2 = next_fast_len(iT + iT - 1)
    pad = np.zeros(np2 - iT) # type: ignore
    k = np.arange(1, iT)
    b = np.concatenate(([1], np.cumprod((k - (d + 1)) / k), pad))

    dx = np.fft.ifft(np.fft.fft(b) * np.fft.fft(np.concatenate((x, pad))))[:iT]
    return np.real(dx)

# Define the rolling fractional differencing function
def rolling_fractional_differencing(group: pd.DataFrame, column, window, d):
    log.debug(f"running rolling_fractional_differencing - group.shape: {group.shape}, column: {column}, window: {window}, d: {d}")
    # Ensure the group is sorted by date (ascending)
    group = group.sort_index()

    result = np.full(group.shape[0], np.nan)  # Initialize result array with NaNs

    # Iterate from the most recent date to the oldest date
    for i in range(len(group), window - 1, -1):
        # The window selection and calculation remain the same
        result[i - 1] = diffseries(group[column].iloc[i - window:i].values, d)[-1]

    return pd.Series(result, index=group.index)


def parallel_rolling_fd(df: pd.DataFrame, column, window, d, group_col=None, n_jobs=4, fd_func=None):
    """
    Parallel implementation of rolling fractional differencing that works with or without grouping.
    
    This function calculates the fractional differencing of a time series using a rolling window
    approach. It can process the entire dataframe at once or split the computation by groups
    for parallel processing.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        The input dataframe containing the time series data.
    column : str
        The name of the column to apply fractional differencing to.
    window : int
        The size of the rolling window for the calculation.
    d : float
        The differencing parameter (typically between 0 and 1).
        - d=0: No differencing (original series)
        - d=1: First-order differencing
        - 0<d<1: Fractional differencing
    group_col : str, optional
        Column name to group by before applying the differencing.
        If None, the entire dataframe is processed as a single series.
    n_jobs : int, default=4
        Number of parallel jobs to run when using grouping.
        Ignored if group_col is None.
    
    Returns:
    --------
    pandas.Series
        A series containing the fractionally differenced values with the same
        index as the input dataframe. Values where there is insufficient history
        (less than window size) will be NaN.
    
    Examples:
    ---------
    # Without grouping - process entire dataframe
    result = parallel_rolling_fd(df, 'Close', window=30, d=0.4)
    
    # With grouping - process each group in parallel
    result = parallel_rolling_fd(df, 'Close', window=30, d=0.4, group_col='expiry', n_jobs=8)
    """
    
    fd_func = funcs['v4']
    start_time = time.perf_counter()
    log.debug(f"running parallel_rolling_fd - column: {column}, window: {window}, d: {d}, group_col: {group_col}, n_jobs: {n_jobs}, fd_func: {fd_func}")
    log.debug(f"start_date: {df['dt'].min()}, end_date: {df['dt'].max()}, len: {len(df)}, expiries: {df['expiry'].sort_values().unique()}")
    df = df.copy()
    
    if group_col is None:
        # No grouping - process the entire dataframe at once
        log.info("No grouping, processing entire dataframe")
        return rolling_fractional_differencing(df, column, window, d)

    # Otherwise, continue with the grouping logic
    groups = list(df.groupby(group_col))
    log.debug(f"Grouped into {len(groups)} groups")

    # Process each group in parallel
    def process_group(name, group_df: pd.DataFrame, fd_func=None):
        
        # log.debug(f"processing_group - {name}, group_df.shape: {group_df.shape}, fd_func: {fd_func}")
        # start_time = time.perf_counter()
        # Ensure the group is sorted by date
        group_df = group_df.sort_index()
        # Use the modified rolling function
        # benchmark.benchmark_and_compare(group_df, column, window, d)
        if fd_func is not None:
            result = fd_func(group_df, column, window, d)
        else:
            result = rolling_fractional_differencing(group_df, column, window, d)
        # log.info(f"complete processing_group:{name} time {round(time.perf_counter() - start_time, 4)}")
        return name, result

    # Execute in parallel
    jobs = []
    for name, group in groups:
        jobs.append(delayed(process_group)(name, group, fd_func))
    results = Parallel(n_jobs=n_jobs)(jobs)
    # results = Parallel(n_jobs=n_jobs)(
    #     delayed(process_group)(name, group, fd_func)
    #     for name, group in groups
    # )

    # Rest remains the same
    # if iterable:
    result_dict = {name: series for name, series in results} # type: ignore
    final_result = pd.concat(result_dict)
    
    log.info(f"complete parallel_rolling_fd with func: {fd_func}, time: {round(time.perf_counter() - start_time, 4)}")
    return final_result.reset_index(level=0, drop=True)



# TODO: FIX THIS
def resample_by_group(df, grouping_col, timeframe='4h'):
    """
    Resample time series data by group with custom market-hours aware binning.
    
    Parameters:
        df: DataFrame with time series data
        grouping_col: Column name to group by
        timeframe: Resampling frequency ('4h', '30min', etc.)
        
    Returns:
        Resampled DataFrame
    """
    # Create a copy to avoid modifying the original
    raise NotImplementedError("This function is not working correctly yet")
    start_time = time.perf_counter()
    log.debug(f"running resample_by_group - grouping_col: {grouping_col}, timeframe: {timeframe}")
    df_copy = df.copy()
    
    # Handle datetime column/index
    if 'dt' not in df_copy.columns:
        if df_copy.index.name == 'dt':
            df_copy = df_copy.reset_index()
        else:
            raise ValueError("Could not find datetime column 'dt'")

    # Parse timeframe once
    if timeframe.endswith('h'):
        bin_hours = float(timeframe[:-1])
    elif timeframe.endswith('min'):
        bin_hours = float(timeframe[:-3]) / 60
    else:
        raise ValueError(f"Unsupported timeframe: {timeframe}")

    # VECTORIZED OPERATIONS:
    # Calculate all time-related columns at once
    df_copy['date'] = df_copy['dt'].dt.normalize()
    df_copy['market_open'] = df_copy['date'] + pd.Timedelta(hours=9, minutes=30)
    
    # Adjust for times before market open
    before_open = df_copy['dt'] < df_copy['market_open']
    if before_open.any():
        df_copy.loc[before_open, 'market_open'] = (
            df_copy.loc[before_open, 'date'] - pd.Timedelta(days=1) + pd.Timedelta(hours=9, minutes=30)
        )
    
    # Calculate bins efficiently
    df_copy['hours_since_open'] = (df_copy['dt'] - df_copy['market_open']).dt.total_seconds() / 3600
    df_copy['bin_number'] = (df_copy['hours_since_open'] // bin_hours).astype(int)
    df_copy['bin'] = df_copy['market_open'] + pd.to_timedelta((df_copy['bin_number'] + 1) * bin_hours, unit='h')
    
    # Define aggregation dictionary - only include columns that exist
    base_agg_dict = {
        "Open": "first",
        "High": "max",
        "Low": "min",
        "Close": "last",
        "Volume": "sum"
    }
    
    # Filter to only include columns that exist in the dataframe
    agg_dict = {k: v for k, v in base_agg_dict.items() if k in df_copy.columns}
    
    # Add other columns with 'last' aggregation
    temp_cols = ['dt', 'bin', 'date', 'market_open', 'hours_since_open', 'bin_number', grouping_col]
    for col in df_copy.columns:
        if col not in agg_dict and col not in temp_cols:
            agg_dict[col] = "last"
    
    # Process each group more efficiently
    groups = df_copy.groupby(grouping_col)
    resampled_dfs = []
    
    for group_value, group in groups:
        # Group by bin
        resampled = group.groupby('bin').agg(agg_dict)
        
        # Only process non-empty groups
        if not resampled.empty:
            # Forward fill and handle Volume in one step
            resampled = resampled.ffill()
            if 'Volume' in resampled.columns:
                resampled['Volume'] = resampled['Volume'].fillna(0)
                
            # Add grouping column
            resampled[grouping_col] = group_value
            resampled_dfs.append(resampled)
    
    # Combine results efficiently
    if not resampled_dfs:
        return pd.DataFrame()
        
    result = pd.concat(resampled_dfs)
    
    # Process the result
    result = result.reset_index()
    result.rename(columns={'bin': 'dt'}, inplace=True)
    
    # Remove duplicates if any (more efficient check)
    if result.columns.duplicated().any():
        result = result.loc[:, ~result.columns.duplicated(keep='first')]
    
    log.info(f"complete resample_by_group time {round(time.perf_counter() - start_time, 4)}")
    return result


def calculate_close_from_extrema(df, grouping_col, high_col='High', low_col='Low',
                                 close_col='Close', window=24, suffix=''):
    """
    Calculate the distance of the closing price from the highest high and lowest low
    within a rolling window.

    Parameters:
        df: DataFrame with price data.
        grouping_col: Column name to group by (e.g., 'expiry').
        high_col: Column name for high prices.
        low_col: Column name for low prices.
        close_col: Column name for close prices.
        window: Look-back window size.
        suffix: Suffix to append to the output column names (e.g., '_1', '_daily').
               Use this to avoid column name conflicts when applying the function
               multiple times with different parameters.

    Returns:
        DataFrame with additional columns:
        - 'dist_from_high{suffix}': Absolute distance from the highest high.
        - 'dist_from_low{suffix}': Absolute distance from the lowest low.
        - 'norm_position{suffix}': Normalized position (0-1) of close between low and high.
    """
    start_time = time.perf_counter()
    log.debug(f"running calculate_close_from_extrema - grouping_col: {grouping_col}, high_col: {high_col}, low_col: {low_col}, close_col: {close_col}, window: {window}, suffix: {suffix}")

    result = df.copy()

    # Define column names with suffix
    dist_high_col = f'dist_from_high{suffix}'
    dist_low_col = f'dist_from_low{suffix}'
    norm_pos_col = f'norm_position{suffix}'

    # Initialize new columns
    result[dist_high_col] = np.nan
    result[dist_low_col] = np.nan
    result[norm_pos_col] = np.nan

    # Process each group separately
    for name, group in df.groupby(grouping_col):
        group = group.sort_index()

        # Calculate rolling highest high and lowest low
        rolling_high = group[high_col].rolling(window, min_periods=1).max()
        rolling_low = group[low_col].rolling(window, min_periods=1).min()

        # Calculate distances
        dist_from_high = rolling_high - group[close_col]
        dist_from_low = group[close_col] - rolling_low

        # Calculate normalized position (0-1) between low and high
        # 0 means at the low, 1 means at the high
        range_size = rolling_high - rolling_low
        # Avoid division by zero when high equals low
        valid_range = range_size > 0
        norm_position = np.where(
            valid_range,
            dist_from_low / range_size,
            0.5  # Default to middle when high equals low
        )

        # Store results
        result.loc[group.index, dist_high_col] = dist_from_high
        result.loc[group.index, dist_low_col] = dist_from_low
        result.loc[group.index, norm_pos_col] = norm_position

    log.info(f"complete calculate_close_from_extrema time {round(time.perf_counter() - start_time, 4)}")
    return result


def summarize_model_performance_sltp(df, stop_loss=None, take_profit=None, adjustable_stop_trigger=None,
                                     trailing_stop_amount=None):
    """
    Simulate trades based on the 'hmm_regime' column and summarize performance with adjustable/trailing stops.

    Trading logic:
      - When flat (position==0) and hmm_regime is -1: enter a short at the Close.
      - When flat and hmm_regime is 1: enter a long at the Close.
      - After any exit (stop, take profit, or signal), wait for a new signal before re-entering:
        - Wait for hmm_regime to change to 0 and then to a new direction, or
        - Wait for hmm_regime to change directly from one direction to another
      - If stop_loss is set and price moves against the position by stop_loss amount, exit with a loss.
      - If adjustable_stop_trigger is set:
        - Initially, the regular stop_loss applies
        - When profit reaches adjustable_stop_trigger, switch to trailing stop mode
        - In trailing stop mode, track the highest (for longs) or lowest (for shorts) price reached
        - Exit when price moves against the high watermark by trailing_stop_amount
      - If take_profit is set, exit when price moves in favor of the position by take_profit amount.
      - Priority: stop_loss > trailing_stop > take_profit

    Parameters:
      - df: DataFrame with columns 'dt', 'Close', 'High', 'Low', and 'hmm_regime'
      - stop_loss: Amount of loss to trigger a stop (None means no stop loss)
      - take_profit: Amount of profit to trigger a take profit (None means no take profit)
      - adjustable_stop_trigger: Minimum profit needed to activate trailing stop (None means no trailing stop)
      - trailing_stop_amount: Amount below/above high watermark to trigger trailing stop exit

    Returns:
      - df_with_pnl: The input DataFrame with additional PnL columns:
        - unrealized_pnl: Shows profit/loss of open positions
        - realized_pnl: Shows profit/loss at trade exit points
        - cumulative_realized_pnl: Running total of realized profits/losses
      - summary: A one-row DataFrame with performance statistics
    """
    import pandas as pd

    # Create a copy of the dataframe to avoid modifying the original
    df_with_pnl = df.copy()

    # Initialize the PnL columns with zeros
    df_with_pnl['unrealized_pnl'] = 0.0
    df_with_pnl['realized_pnl'] = 0.0

    # Ensure the last row has hmm_regime=0 to close any open positions
    df_with_pnl.loc[df_with_pnl.index[-1], 'hmm_regime'] = 0

    trades = []
    position = 0  # 0 means flat, 1 means long, -1 means short
    entry_price = None
    entry_dt = None
    trade_position = None  # store the type of trade for pnl calculation
    stops_taken = 0
    take_profits_taken = 0
    trailing_stops_taken = 0
    signal_exits = 0

    # Additional variables for trailing stop
    high_watermark = None
    trailing_stop_active = False
    prev_regime = None  # Track previous regime to detect changes

    # Flag to enforce proper re-entry logic after exits
    waiting_for_new_signal = False
    last_trade_direction = 0  # Direction of the last trade (1 or -1)

    # Ensure dt is a datetime
    if not pd.api.types.is_datetime64_any_dtype(df_with_pnl['dt']):
        df_with_pnl['dt'] = pd.to_datetime(df_with_pnl['dt'])

    # Assume df is sorted by dt in ascending order.
    for i, row in enumerate(df_with_pnl.itertuples()):
        current_regime = row.hmm_regime

        # If in a position, first check if the regime has changed
        if position != 0 and entry_price is not None:
            # Check if regime has changed from one direction to another or to zero
            regime_exit = False

            # Exit if regime changes to 0 or opposite direction of current position
            if (current_regime == 0 or
                    (position == 1 and current_regime == -1) or
                    (position == -1 and current_regime == 1)):
                regime_exit = True

            if regime_exit:
                exit_price = row.Close
                exit_dt = row.dt
                # Calculate profit/loss based on direction
                if trade_position == 1:
                    pnl = exit_price - entry_price
                else:
                    pnl = entry_price - exit_price
                duration = exit_dt - entry_dt

                # Update realized_pnl at exit
                df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                # Reset unrealized_pnl to 0 as we've closed the position
                df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                trades.append({
                    'entry_dt': entry_dt,
                    'exit_dt': exit_dt,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'duration': duration,
                    'position': trade_position,
                    'exit_reason': 'signal_change'
                })
                signal_exits += 1

                # Reset state
                position = 0
                entry_price = None
                entry_dt = None
                last_trade_direction = trade_position  # Store the last direction we traded
                trade_position = None
                high_watermark = None
                trailing_stop_active = False

                # Do not set waiting_for_new_signal flag if we're going directly from one regime to the opposite
                # This allows immediate reversal from long to short or short to long
                waiting_for_new_signal = current_regime == 0

                # If regime changed directly from one direction to another, enter new position immediately
                if current_regime != 0 and current_regime != last_trade_direction:
                    position = current_regime
                    entry_price = row.Close
                    entry_dt = row.dt
                    trade_position = current_regime
                    high_watermark = row.Close
                    trailing_stop_active = False

                # Continue to next row as we've already processed this one
                prev_regime = current_regime
                continue

        # If still in a position, check for stop loss, take profit, or trailing stop
        if position != 0 and entry_price is not None:
            # Calculate current profit/loss
            if position == 1:  # Long position
                current_profit = row.High - entry_price  # Maximum potential profit in this bar
                # Update unrealized_pnl column with current unrealized profit/loss based on Close price
                df_with_pnl.at[row.Index, 'unrealized_pnl'] = row.Close - entry_price

                # Update high watermark if needed
                if high_watermark is None or row.High > high_watermark:
                    high_watermark = row.High

                # Check if adjustable stop trigger has been hit
                if (adjustable_stop_trigger is not None and
                        not trailing_stop_active and
                        current_profit >= adjustable_stop_trigger):
                    trailing_stop_active = True

                # Check for trailing stop hit (if active)
                trailing_stop_hit = (trailing_stop_active and
                                     trailing_stop_amount is not None and
                                     row.Low <= high_watermark - trailing_stop_amount)

                # Check if regular stop loss hit (if trailing stop not active)
                stop_loss_hit = (not trailing_stop_active and
                                 stop_loss is not None and
                                 row.Low <= entry_price - stop_loss)

                # Check if take profit hit
                take_profit_hit = (take_profit is not None and
                                   row.High >= entry_price + take_profit)

                # Exit conditions with priorities
                if stop_loss_hit:
                    exit_price = entry_price - stop_loss  # Assume filled at stop loss price
                    exit_dt = row.dt
                    # Loss is negative 
                    pnl = -stop_loss  # type: ignore
                    duration = exit_dt - entry_dt

                    # Update realized_pnl at exit
                    df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                    # Reset unrealized_pnl to 0 as we've closed the position
                    df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                    trades.append({
                        'entry_dt': entry_dt,
                        'exit_dt': exit_dt,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'duration': duration,
                        'position': trade_position,
                        'exit_reason': 'stop_loss'
                    })
                    stops_taken += 1
                    # Reset state
                    position = 0
                    entry_price = None
                    entry_dt = None
                    last_trade_direction = trade_position  # Store the last direction we traded
                    trade_position = None
                    high_watermark = None
                    trailing_stop_active = False
                    waiting_for_new_signal = True  # Need a new signal to re-enter

                elif trailing_stop_hit:
                    exit_price = high_watermark - trailing_stop_amount  # Assume filled at trailing stop price
                    exit_dt = row.dt
                    pnl = exit_price - entry_price
                    duration = exit_dt - entry_dt

                    # Update realized_pnl at exit
                    df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                    # Reset unrealized_pnl to 0 as we've closed the position
                    df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                    trades.append({
                        'entry_dt': entry_dt,
                        'exit_dt': exit_dt,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'duration': duration,
                        'position': trade_position,
                        'exit_reason': 'trailing_stop'
                    })
                    trailing_stops_taken += 1
                    # Reset state
                    position = 0
                    entry_price = None
                    entry_dt = None
                    last_trade_direction = trade_position  # Store the last direction we traded
                    trade_position = None
                    high_watermark = None
                    trailing_stop_active = False
                    waiting_for_new_signal = True  # Need a new signal to re-enter

                elif take_profit_hit:
                    exit_price = entry_price + take_profit  # Assume filled at take profit price
                    exit_dt = row.dt
                    pnl = take_profit  # Profit is positive
                    duration = exit_dt - entry_dt

                    # Update realized_pnl at exit
                    df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                    # Reset unrealized_pnl to 0 as we've closed the position
                    df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                    trades.append({
                        'entry_dt': entry_dt,
                        'exit_dt': exit_dt,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'duration': duration,
                        'position': trade_position,
                        'exit_reason': 'take_profit'
                    })
                    take_profits_taken += 1
                    # Reset state
                    position = 0
                    entry_price = None
                    entry_dt = None
                    last_trade_direction = trade_position  # Store the last direction we traded
                    trade_position = None
                    high_watermark = None
                    trailing_stop_active = False
                    waiting_for_new_signal = True  # Need a new signal to re-enter

            elif position == -1:  # Short position
                current_profit = entry_price - row.Low  # Maximum potential profit in this bar
                # Update unrealized_pnl column with current unrealized profit/loss based on Close price
                # For short positions, profit occurs when price goes down, so we use entry_price - Close
                df_with_pnl.at[row.Index, 'unrealized_pnl'] = entry_price - row.Close

                # Update high watermark (which is actually a "low watermark" for shorts)
                if high_watermark is None or row.Low < high_watermark:
                    high_watermark = row.Low

                # Check if adjustable stop trigger has been hit
                if (adjustable_stop_trigger is not None and
                        not trailing_stop_active and
                        current_profit >= adjustable_stop_trigger):
                    trailing_stop_active = True

                # Check for trailing stop hit (if active)
                trailing_stop_hit = (trailing_stop_active and
                                     trailing_stop_amount is not None and
                                     row.High >= high_watermark + trailing_stop_amount)

                # Check if regular stop loss hit (if trailing stop not active)
                stop_loss_hit = (not trailing_stop_active and
                                 stop_loss is not None and
                                 row.High >= entry_price + stop_loss)

                # Check if take profit hit
                take_profit_hit = (take_profit is not None and
                                   row.Low <= entry_price - take_profit)

                # Exit conditions with priorities
                if stop_loss_hit:
                    exit_price = entry_price + stop_loss  # Assume filled at stop loss price
                    exit_dt = row.dt
                    # Loss is negative
                    pnl = -stop_loss  # type: ignore
                    duration = exit_dt - entry_dt

                    # Update realized_pnl at exit
                    df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                    # Reset unrealized_pnl to 0 as we've closed the position
                    df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                    trades.append({
                        'entry_dt': entry_dt,
                        'exit_dt': exit_dt,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'duration': duration,
                        'position': trade_position,
                        'exit_reason': 'stop_loss'
                    })
                    stops_taken += 1
                    # Reset state
                    position = 0
                    entry_price = None
                    entry_dt = None
                    last_trade_direction = trade_position  # Store the last direction we traded
                    trade_position = None
                    high_watermark = None
                    trailing_stop_active = False
                    waiting_for_new_signal = True  # Need a new signal to re-enter

                elif trailing_stop_hit:
                    exit_price = high_watermark + trailing_stop_amount  # Assume filled at trailing stop price
                    exit_dt = row.dt
                    pnl = entry_price - exit_price
                    duration = exit_dt - entry_dt

                    # Update realized_pnl at exit
                    df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                    # Reset unrealized_pnl to 0 as we've closed the position
                    df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                    trades.append({
                        'entry_dt': entry_dt,
                        'exit_dt': exit_dt,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'duration': duration,
                        'position': trade_position,
                        'exit_reason': 'trailing_stop'
                    })
                    trailing_stops_taken += 1
                    # Reset state
                    position = 0
                    entry_price = None
                    entry_dt = None
                    last_trade_direction = trade_position  # Store the last direction we traded
                    trade_position = None
                    high_watermark = None
                    trailing_stop_active = False
                    waiting_for_new_signal = True  # Need a new signal to re-enter

                elif take_profit_hit:
                    exit_price = entry_price - take_profit  # Assume filled at take profit price
                    exit_dt = row.dt
                    pnl = take_profit  # Profit is positive
                    duration = exit_dt - entry_dt

                    # Update realized_pnl at exit
                    df_with_pnl.at[row.Index, 'realized_pnl'] = pnl
                    # Reset unrealized_pnl to 0 as we've closed the position
                    df_with_pnl.at[row.Index, 'unrealized_pnl'] = 0.0

                    trades.append({
                        'entry_dt': entry_dt,
                        'exit_dt': exit_dt,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'duration': duration,
                        'position': trade_position,
                        'exit_reason': 'take_profit'
                    })
                    take_profits_taken += 1
                    # Reset state
                    position = 0
                    entry_price = None
                    entry_dt = None
                    last_trade_direction = trade_position  # Store the last direction we traded
                    trade_position = None
                    high_watermark = None
                    trailing_stop_active = False
                    waiting_for_new_signal = True  # Need a new signal to re-enter

        # If flat, look for an entry signal considering the waiting_for_new_signal flag
        elif position == 0:
            # Determine if this is a valid new signal situation
            valid_new_signal = False

            if waiting_for_new_signal:
                # After a stop/take profit exit, need a regime change to enter
                if prev_regime is not None:
                    # Scenario 1: Change from 0 to non-zero (new trend)
                    if prev_regime == 0 and current_regime != 0:
                        valid_new_signal = True
                    # Scenario 2: Change from one direction to opposite direction
                    elif prev_regime != 0 and current_regime != 0 and prev_regime != current_regime:
                        valid_new_signal = True
                    # Scenario 3: Change from non-zero to 0 (reset condition)
                    elif prev_regime != 0 and current_regime == 0:
                        # Just reset the waiting flag, but don't enter a trade
                        waiting_for_new_signal = False
            else:
                # Normal entry condition when not waiting
                valid_new_signal = current_regime != 0

            # Enter a trade if we have a valid signal
            if valid_new_signal:
                position = current_regime
                entry_price = row.Close
                entry_dt = row.dt
                trade_position = current_regime
                high_watermark = row.Close
                trailing_stop_active = False
                waiting_for_new_signal = False  # Reset the waiting flag

        prev_regime = current_regime

    # Create a DataFrame for the completed trades.
    if not trades:
        return df_with_pnl, pd.DataFrame()

    trades_df = pd.DataFrame(trades)

    total_trades = len(trades_df)
    win_trades = trades_df[trades_df.pnl > 0]
    win_pct = (len(win_trades) / total_trades) * 100 if total_trades > 0 else 0
    avg_duration = trades_df.duration.mean()
    longest_trade = trades_df.duration.max()
    max_win = trades_df.pnl.max()
    max_loss = trades_df.pnl.min()
    avg_win = win_trades.pnl.mean() if not win_trades.empty else 0
    total_pnl = trades_df.pnl.sum()
    start_date = trades_df['entry_dt'].min()
    end_date = trades_df['exit_dt'].max()

    summary = pd.DataFrame({
        'Total Trades': [total_trades],
        'Win%': [win_pct],
        'Average Duration': [avg_duration],
        'Longest Trade': [longest_trade],
        'Max Win': [max_win],
        'Max Loss': [max_loss],
        'Average Win': [avg_win],
        'Total PnL': [total_pnl],
        'Stops Taken': [stops_taken],
        'Take Profits Taken': [take_profits_taken],
        'Trailing Stops Taken': [trailing_stops_taken],
        'Signal Exits': [signal_exits],
        'Start Date': [start_date],
        'End Date': [end_date]
    })

    # Calculate cumulative realized P&L
    df_with_pnl['cumulative_realized_pnl'] = df_with_pnl['realized_pnl'].cumsum()

    return df_with_pnl, summary



