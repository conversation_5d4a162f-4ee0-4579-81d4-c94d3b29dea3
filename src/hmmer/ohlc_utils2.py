import time
import logging

import pandas as pd
import numpy as np

log = logging.getLogger(__name__)


pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 500)
pd.set_option('display.width', 1000)

#Helper function for add expiry position
# def calculate_expiry_dates(date_range, change_day, exit_mths, num_columns):
def calculate_expiry_dates(start_date, end_date, change_day, exit_mths, num_columns):
    """
    Calculate forward expiry dates for a given date range.
    
    Parameters:
        date_range: pandas DatetimeIndex of trade dates
        change_day: int, days to shift the expiry date
        exit_mths: list of int, valid expiry months
        num_columns: int, number of forward periods to calculate
    
    Returns:
        DataFrame with Date column and V1-Vn columns containing expiry dates
    
    Example:
        >>> date_range = pd.date_range('2023-01-01', '2023-01-03')
        >>> change_day = 15
        >>> exit_mths = [3, 6, 9, 12]  # March, June, September, December
        >>> num_columns = 3
        >>> calculate_expiry_dates(date_range, change_day, exit_mths, num_columns)
           Date         V1          V2          V3
        0  2023-01-01  2023-03-15  2023-06-15  2023-09-15
        1  2023-01-02  2023-03-15  2023-06-15  2023-09-15
        2  2023-01-03  2023-03-15  2023-06-15  2023-09-15
    """
    # Extend range to ensure complete coverage
    start_time = time.perf_counter()
    log.debug(f"running calculate_expiry_dates - start_date: {start_date}, end_date: {end_date}, change_day: {change_day}, exit_mths: {exit_mths}, num_columns: {num_columns}")
    date_range = pd.date_range(start_date, end_date, freq='D')
    extended_range = pd.date_range(
        date_range[0] - pd.DateOffset(years=1),
        date_range[-1] + pd.DateOffset(years=20),
        freq='D'
    )

    # Get candidate expiry dates in one step
    candidates = (extended_range.to_period('M').to_timestamp() + 
                 pd.to_timedelta(change_day, unit='D'))
    
    # Filter valid months and get unique dates
    candidates = candidates[candidates.month.isin(exit_mths)].unique()
    candidates = pd.DatetimeIndex(candidates)  # Convert to DatetimeIndex

    # Calculate forward periods using vectorized operations
    indices = np.searchsorted(candidates, date_range)
    forward_indices = (indices.reshape(-1, 1) + 
                      np.arange(num_columns).reshape(1, -1))
    
    # Convert candidates to numpy array before indexing
    candidates_array = candidates.to_numpy()
    forward_dates = (candidates_array[forward_indices] - 
                    pd.to_timedelta(change_day, unit='D'))

    log.info(f"complete calculate_expiry_dates time {round(time.perf_counter() - start_time, 4)}")
    return pd.DataFrame(
        forward_dates,
        columns=[f'V{i+1}' for i in range(num_columns)],
        index=date_range
    ).reset_index(names='Date')


def add_expiry_position(
    df,
    shiftdays,
    trade_date_col='TradeDate',
    trade_year_col='expiry_yr',  # used for the contract code (e.g. "2025")
    trade_month_col='expiry_mth',  # used for the contract month number (e.g. "12")
):
    """
    Add a forward period indicator column ("fwdper") to the dataframe by matching each
    trade's contract code with a pre‐computed lookup table of forward expiry dates.

    Parameters:
      df: pandas DataFrame containing trade/price data (pre-filtered for specific product)
      shiftdays: int, number of days to shift the expiry date (e.g. -5, 10)
      trade_date_col: name of the column holding the trade date (will be normalized to remove any time)
      trade_year_col: name of the column holding the expiry "year" (used to create the contract code)
      trade_month_col: name of the column holding the expiry "month" number (used to create the contract code)

    Returns:
        DataFrame: Input DataFrame with a new column "fwdper"
    """
    start_time = time.perf_counter()
    log.debug(f"running add_expiry_position - shiftdays: {shiftdays}, trade_date_col: {trade_date_col}, trade_year_col: {trade_year_col}, trade_month_col: {trade_month_col}")
    
    # Work on a copy so that the original is not modified
    df = df.copy()

    # Ensure the trade date is in datetime format and "normalized" (i.e. no time component)
    df[trade_date_col] = pd.to_datetime(df[trade_date_col]).dt.normalize()

    # Create the contract code column ("exp_yr_m") by concatenating the year and month fields
    df['exp_yr_m'] = pd.to_datetime(
        df[trade_year_col].astype(int).astype(str) + '-' +
        df[trade_month_col].astype(int).astype(str).str.zfill(2) + '-01'
    )

    # Get the overall trade date range
    start_date = df[trade_date_col].min()
    end_date = df[trade_date_col].max()
    duration = end_date - start_date
    log.debug(f"overall trade date range: {start_date} to {end_date} - {duration.days} days")
    # date_range = pd.date_range(start_date, end_date, freq='D')

    num_columns = 20  # number of forward expiry dates to compute

    # Get the unique expiry month numbers
    expiry_months = np.sort(df[trade_month_col].unique())
    
    # Compute the "exit months"
    exit_mths = sorted({
        (pd.Timestamp(year=2000, month=int(m), day=1) + pd.Timedelta(days=shiftdays)).month
        for m in expiry_months
    })

    # Calculate the forward expiry dates lookup table
    expiry_df = calculate_expiry_dates(start_date, end_date, shiftdays, exit_mths, num_columns)

    # Reshape the lookup table to long format
    expiry_long = expiry_df.melt(id_vars='Date', var_name='fwdper', value_name='exp_yr_m')
    expiry_long['fwdper'] = expiry_long['fwdper'].str.replace('V', '').astype(int)
    expiry_long = expiry_long.rename(columns={'Date': trade_date_col})

    # Merge with lookup table
    merged = pd.merge(df, expiry_long, on=[trade_date_col, 'exp_yr_m'], how='left')
    merged = merged[~merged['fwdper'].isna()].copy()
    merged['fwdper'] = merged['fwdper'].astype(int)

    # Drop temporary column and reset index
    merged = merged.drop('exp_yr_m', axis=1).reset_index(drop=True)
    
    # move fwdper column before Open
    merged = merged[['dt', 'TradeDate', 'prd', 'name2', 'expiry', 'expiry_yr', 'expiry_mth', 'fwdper', 'Open', 'High', 'Low', 'Close', 'Volume']]

    log.info(f"complete add_expiry_position time {round(time.perf_counter() - start_time, 4)}")
    return merged


def create_continuous_futures(prdf):
    start_time = time.perf_counter()
    log.debug("running create_continuous_futures")
    # Filter for relevant forward periods and reset index
    prdf1 = prdf[prdf['fwdper'].isin([1, 2])].copy().reset_index(drop=True)

    # Calculate days to expiry for each group
    prdf1['daystolast'] = prdf1.groupby('expiry')['date'].transform(lambda x: (x.max() - x).dt.days + 1)

    # Pivot prdf1 to wide format
    prdf_wide = prdf1.pivot(index='date', columns='fwdper', values=['Close', 'daystolast']).reset_index()

    # Flatten the multi-index columns
    prdf_wide.columns = [f'{j}_{i}' if j else i for i, j in prdf_wide.columns]
    prdf_wide = prdf_wide.rename(columns={'1_Close': 'Close_1', '2_Close': 'Close_2',
                                          '1_daystolast': 'daystolast_1', '2_daystolast': 'daystolast_2'})

    # Fill down missing values
    prdf_wide.fillna(method='ffill', inplace=True)

    # Identify roll dates
    roll_dates = prdf_wide[prdf_wide['daystolast_1'] == 1][['date', 'Close_1', 'Close_2']]
    roll_dates = roll_dates.iloc[:-1].copy() # Exclude the most recent roll date

    # Calculate the adjustment ratios on roll dates
    roll_dates['ratio'] = roll_dates['Close_2'] / roll_dates['Close_1']

    # Initialize the continuous futures series
    prdf_wide['continuous_futures'] = prdf_wide['Close_1']

    # Adjust the historical prices by the ratios
    for i in range(len(roll_dates)):
        roll_date = roll_dates.iloc[i]['date']
        adjustment_ratio = roll_dates.iloc[i]['ratio']
        prdf_wide.loc[prdf_wide['date'] <= roll_date, 'continuous_futures'] *= adjustment_ratio

    log.debug(f"complete create_continuous_futures time {round(time.perf_counter() - start_time, 4)}")
    return prdf_wide



def create_return_series(df: pd.DataFrame, return_col: str='Close', groupby_col: str='expiry', dropna: bool=True) -> pd.Series:
    """
    Create a return series from a dataframe by grouping by a column and calculating the returns.

    Parameters:
        df (pd.DataFrame): The dataframe to use.
        return_col (str): The column to use for the returns.
        groupby_col (str): The column to group by.

    Returns:
        pd.Series: The return series.
    """
    ret = df.groupby(groupby_col)[return_col].transform(lambda x: x.diff())
    if dropna:
        ret = ret.dropna()
    return ret


def trim_excess_contracts(df: pd.DataFrame, groupby_col: str='expiry', fwdper_col: str='fwdper', fwdper_value: int=1) -> pd.DataFrame:
    """
    Trim contracts that do not have a forward period of fwdper_value (1 by default).

    Parameters:
        df (pd.DataFrame): The dataframe to use.
        groupby_col (str): The column to group by.
        fwdper_col (str): The column holding the forward period.

    Returns:
        pd.DataFrame: The trimmed dataframe.
    """
    valid_expiries = df.groupby(groupby_col)[fwdper_col].apply(lambda x: (x == fwdper_value).any())
    df = df[df[groupby_col].isin(valid_expiries[valid_expiries].index)].reset_index(drop=True)
    return df