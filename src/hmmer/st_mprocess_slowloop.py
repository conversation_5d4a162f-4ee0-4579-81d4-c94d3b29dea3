import os
import time
import logging
from datetime import datetime
from zoneinfo import ZoneInfo

# import joblib
import pandas as pd
from pandas.tseries.offsets import CustomBusinessDay
from pandas.tseries.holiday import USFederalHolidayCalendar

# from tqdm import tqdm

from hmmer import db
from hmmer.ohlc_utils2 import add_expiry_position, create_return_series, trim_excess_contracts
from hmmer.st_mprocess_models import add_hmm_regime_fc_fwd
from hmmer.st_mprocess_helper2 import parallel_rolling_fd
# from hmmer.st_mprocess_helper import evaluate_performance
from hmmer.st_mprocess_helper import calculate_close_from_extrema
# from hmmer.st_mprocess_helper2 import calculate_close_from_extrema as calculate_close_from_extrema2
from hmmer.st_mprocess_helper import resample_by_group
# from hmmer.st_mprocess_helper2 import resample_by_group as resample_by_group2
# from hmmer.st_mprocess_parallel import parallel_processing_with_joblib, process_expiry

log = logging.getLogger(__name__)

PG_CLIENT = os.getenv("POSTGRES_CLIENT")

STARTING_YEAR = 2018

# Total number of months to get data for per contract
CT_MNTHS_BACK = 18

# Number of jobs for parallel processing
N_JOBS = 16

TABLE_NAME = "hmmer.allbars"

def run(prd: str, record_results: bool = True):
    timings = []
    start_time = time.perf_counter()

    p = db.get_parameter(prd)

    shiftdays = p.shiftdays
    shifttime = p.shifttime

    fd1_window = p.fd1_window
    fd1_d = p.fd1_d
    fd2_window = p.fd2_window
    fd2_d = p.fd2_d

    norm_position_window = p.norm_position_window
    smoothing_window = p.smoothing_window
    smoothing_alpha = p.smoothing_alpha
    mthsback = p.mthsback
    covariance_type = p.covariance_type
    window_size = p.window_size
    sticky_factor = p.sticky_factor
    forward_bars = p.forward_bars

    # stop = p.stop
    # stopadj = p.stopadj
    # trail = p.trail

    open_time = p.eopen_est
    # close_time = p.eclose_est
    runtimestamp = datetime.now().astimezone(ZoneInfo("America/New_York"))
    us_bd = CustomBusinessDay(calendar=USFederalHolidayCalendar())

    #Get DTN data from database
    stime = time.perf_counter()
    # years_back = mthsback // 12 + 1
    idata  = db.load_hmmdb_data(prd, runtimestamp, shifttime, ct_mnths_back=CT_MNTHS_BACK)
    timings.append(('load_data', round(time.perf_counter() - stime, 2)))
    
    
    
    ##### masking #####
    stime = time.perf_counter()
    is_sunday = idata['dt'].dt.weekday == 6
    idata = idata[~(is_sunday & (idata['dt'].dt.time < open_time))]
    
    # base TradeDate on the calendar date
    idata['TradeDate'] = idata['dt'].dt.date
    
    # Find the most recent calendar date in the data
    most_recent_date = idata['dt'].dt.date.max()

    # map each date to the *next* date that actually exists in the data
    uniq = sorted(idata['TradeDate'].unique())
    next_day = {d: uniq[i + 1] if i < len(uniq) - 1 else d
                for i, d in enumerate(uniq)}

    # shift rows that belong to the next session
    mask = (
            (idata['dt'].dt.time >= shifttime)  # after TAS, any weekday
            | ((is_sunday) & (idata['dt'].dt.time >= open_time))  # Sunday evening
    )
    
    # Split mask into historical and most recent
    historical_mask = mask & (idata['dt'].dt.date < most_recent_date)
    recent_mask = mask & (idata['dt'].dt.date == most_recent_date)
    
    # For historical dates, map to next day in data
    if historical_mask.any():
        idata.loc[historical_mask, 'TradeDate'] = idata.loc[historical_mask, 'TradeDate'].map(next_day)

    # For the most recent date, use CustomBusinessDay projection
    if recent_mask.any():
        dates_to_shift = pd.to_datetime(idata.loc[recent_mask, 'TradeDate'])
        next_dates = dates_to_shift + us_bd
        idata.loc[recent_mask, 'TradeDate'] = next_dates.dt.date
        
    # Handle Sundays - split similarly
    sunday_mask = idata['TradeDate'].apply(lambda d: d.weekday() == 6)
    historical_sunday = sunday_mask & (idata['dt'].dt.date < most_recent_date)
    recent_sunday = sunday_mask & (idata['dt'].dt.date == most_recent_date)

    # Historical Sundays use mapping
    if historical_sunday.any():
        idata.loc[historical_sunday, 'TradeDate'] = idata.loc[historical_sunday, 'TradeDate'].map(next_day)

    # Recent Sundays use projection
    if recent_sunday.any():
        sunday_dates = pd.to_datetime(idata.loc[recent_sunday, 'TradeDate'])
        next_dates = sunday_dates + us_bd
        idata.loc[recent_sunday, 'TradeDate'] = next_dates.dt.date
    timings.append(('masking', round(time.perf_counter() - stime, 2)))
    ##### /masking #####


    # products_with_change_days = [(prds[0], shiftdays)]
    stime = time.perf_counter()
    idata = add_expiry_position(idata,
                                #  products_with_change_days,
                                shiftdays,
                                trade_date_col='TradeDate',
                                #  product_col='prd',
                                trade_year_col='expiry_yr',
                                trade_month_col='expiry_mth')
    timings.append(('add_expiry_position', round(time.perf_counter() - stime, 2)))


    # stime = time.perf_counter()
    # valid_expiries = idata[idata['fwdper'] == 1]['expiry'].sort_values().unique() 
    # # max_expiries = valid_expiries[-2:]
    # log.info(f"max expiry: {valid_expiries.max()}")
    # idata = idata[idata['expiry'].isin(valid_expiries)].reset_index(drop=True)
    # log.info(f"max idata expiry: {idata['expiry'].max()}")
    # timings.append(('filter_expiries', round(time.perf_counter() - stime, 2)))



    stime = time.perf_counter()
    idata_init = idata.copy()
    idata_init.sort_values(by=['dt'], inplace=True)
    price_fd1_series = parallel_rolling_fd(
        idata_init,
        group_col='expiry',
        column='Close',
        window=fd1_window,
        d=fd1_d,
        n_jobs=N_JOBS
    ).round(4)
    timings.append(('price_fd1', round(time.perf_counter() - stime, 2)))

    stime = time.perf_counter()
    price_fd2_series = parallel_rolling_fd(
        idata_init,
        group_col='expiry',
        column='Close',
        window=fd2_window,
        d=fd2_d,
        n_jobs=N_JOBS
    ).round(4)
    timings.append(('price_fd2', round(time.perf_counter() - stime, 2)))

    idata_init['price_fd1'] = price_fd1_series
    idata_init['price_fd2'] = price_fd2_series


    #resample
    stime = time.perf_counter()
    idata_fd = idata_init.copy()
    orig_columns = idata_fd.columns.tolist()
    idata_fd = resample_by_group(idata_fd,'expiry','4h')
    # Reorder columns to match original order (for columns that still exist)
    preserved_columns = [col for col in orig_columns if col in idata_fd.columns]
    idata_fd = idata_fd[preserved_columns + [col for col in idata_fd.columns if col not in preserved_columns]]
    timings.append(('resample_by_group', round(time.perf_counter() - stime, 2)))


    stime = time.perf_counter()
    idata_fd = calculate_close_from_extrema(
        idata_fd,
        'expiry',
        'High',
        'Low',
        'Close',
        norm_position_window,
        ''
    )
    timings.append(('calculate_close_from_extrema', round(time.perf_counter() - stime, 2)))



    # idata_fd['return'] = idata_fd.groupby('expiry')['Close'].transform(lambda x: x.diff())
    # idata_fd = idata_fd.dropna().reset_index(drop=True)


    idata_fd['return'] = create_return_series(idata_fd)

    # trim excess contracts
    idata_fd = trim_excess_contracts(idata_fd)
    # valid_expiries = idata_fd.groupby('expiry')['fwdper'].apply(lambda x: (x == 1).any())
    # idata_fd = idata_fd[idata_fd['expiry'].isin(valid_expiries[valid_expiries].index)].reset_index(drop=True)


    feature_columns=['price_fd1','price_fd2','norm_position']

    uexpiries = idata_fd[idata_fd['expiry'] >= 201801]['expiry'].unique()


    # stime = time.perf_counter()
    # allbars = pd.DataFrame()
    # results = joblib.Parallel(n_jobs=N_JOBS, verbose=10)(
    #     joblib.delayed(add_hmm_regime_fc_fwd)(
    #         idata_fd[(idata_fd['expiry'] <= uexpiry) & (idata_fd['fwdper'] == 1)].copy().reset_index(drop=True),
    #         uexpiry,
    #         feature_columns=feature_columns,
    #         smoothing_window=smoothing_window,
    #         smoothing_alpha=smoothing_alpha,
    #         covariance_type=covariance_type,
    #         mthsback=mthsback,
    #         window_size=window_size,
    #         sticky_factor=sticky_factor,
    #         fwdper=1,
    #         forward_bars=forward_bars
    #     ) for uexpiry in uexpiries if 1 in idata_fd[(idata_fd['expiry'] == uexpiry) & (idata_fd['fwdper'] == 1)]['fwdper'].values
    # )
    # jobs = []
    # for uexpiry in uexpiries:
    #     if 1 in idata_fd[(idata_fd['expiry'] == uexpiry) & (idata_fd['fwdper'] == 1)]['fwdper'].values:
    #         job = joblib.delayed(add_hmm_regime_fc_fwd)(
    #             idata_fd[(idata_fd['expiry'] <= uexpiry) & (idata_fd['fwdper'] == 1)].copy().reset_index(drop=True),
    #             uexpiry,
    #             feature_columns=feature_columns,
    #             smoothing_window=smoothing_window,
    #             smoothing_alpha=smoothing_alpha,
    #             covariance_type=covariance_type,
    #             mthsback=mthsback,
    #             window_size=window_size,
    #             sticky_factor=sticky_factor,
    #             fwdper=1,
    #             forward_bars=forward_bars
    #         )
    #         jobs.append(job)
    # results = joblib.Parallel(n_jobs=N_JOBS, verbose=10)(jobs)
    # timings.append(('parallel_processing', round(time.perf_counter() - stime, 2)))

    stime = time.perf_counter()
    results = []
    uexpiry = idata_fd.loc[idata_fd['fwdper'] == 1, 'expiry'].max()
    log.info(f"max uexpiry: {uexpiry}")
    result = add_hmm_regime_fc_fwd(
        idata_fd[(idata_fd['expiry'] <= uexpiry) & (idata_fd['fwdper'] == 1)].copy().reset_index(drop=True),
        uexpiry,
        feature_columns=feature_columns,
        smoothing_window=smoothing_window,
        smoothing_alpha=smoothing_alpha,
        covariance_type=covariance_type,
        mthsback=mthsback,
        window_size=window_size,
        sticky_factor=sticky_factor,
        fwdper=1,
        forward_bars=forward_bars
    )
    results.append(result)
    timings.append(('hmm processing', round(time.perf_counter() - stime, 2)))
    
    # remove last bar from all contracts except current one
    results = [list(r) for r in results] # generator to list # type: ignore
    for processed_data in results[:-1]:
        if processed_data and len(processed_data) > 0:
            processed_data[0] = processed_data[0].iloc[:-1] # type: ignore

    # Process results and combine into allbars
    stime = time.perf_counter()
    allbars = pd.DataFrame()
    for processed_data in results:
        if processed_data and len(processed_data) > 0:
            expiry_bars = processed_data[0].reset_index(drop=True)
            if 'expiry' not in expiry_bars.columns:
                # Get the expiry from the data
                expiry = expiry_bars['expiry'].iloc[0] if 'expiry' in expiry_bars.columns else None
                if expiry is None:
                    # Try to infer from the data structure
                    for uexpiry in uexpiries:
                        if processed_data[0]['expiry'].iloc[0] == uexpiry:
                            expiry = uexpiry
                            break
                expiry_bars['expiry'] = expiry
            allbars = pd.concat([allbars, expiry_bars], ignore_index=True)
    timings.append(('combine_allbars', round(time.perf_counter() - stime, 2)))



    # # Step 3: Example - Evaluate performance with specific stop parameters
    # allsummary = evaluate_performance(
    #     allbars,
    #     stop_loss=stop,
    #     adjustable_stop_trigger=stopadj,
    #     trailing_stop_amount=trail
    # )
    # #allsummary = evaluate_performance(allbars)
    # summaries = allsummary[0]
    # smrybars = allsummary[1]

    # yearly_summary = summaries.groupby('year').agg({
    #     'Win%': 'mean',
    #     'Total Trades': ['median', 'std'],
    #     'Duration_Hours': 'mean',
    #     'Max Win': 'max',
    #     'Max Loss': 'min',
    #     'Total PnL': 'sum',
    #     'Average Win': 'mean'
    # })

    runtime = round(time.perf_counter() - start_time, 2)
    timings.append(('total', runtime))
    ddf = pd.DataFrame(timings, columns=['step', 'runtime'])

    log.info(ddf)

    # dbparams = yaml.safe_load(Path("connections.yaml").read_text())["connections"][PG_CLIENT]
    # url = (f"postgresql+psycopg://{dbparams['user']}:{dbparams['password']}@{dbparams['host']}:{dbparams['port']}/{dbparams['database']}"
    #         + ("?sslmode=require" if dbparams.get("ssl") else ""))
    # engine = create_engine(url)

    if record_results:
        import tradertools as tt
        
        allbars['TradeDate'] = allbars['dt'].dt.date
        pg = tt.clients.get_pg_client(PG_CLIENT)
        data = allbars.to_dict(orient='records')
        log.info(f"Inserting {prd} into {TABLE_NAME}, start: {data[0]['dt']}, end: {data[-1]['dt']}")
        pg.upsert(TABLE_NAME, data, conflict_target=['dt', 'name2']) # type: ignore

