import time
import logging
import numpy as np
import pandas as pd

from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler

log = logging.getLogger(__name__)


def smooth_states_weighted(states, window=5, alpha=0.7):
    """Vectorized implementation of weighted smoothing"""
    n = len(states)
    smoothed = np.zeros_like(states)

    # Create weight matrices once (huge performance gain)
    max_window = min(window, n)
    weights = np.power(alpha, np.arange(max_window - 1, -1, -1))

    # Process in larger chunks
    for i in range(0, n, 100):
        end = min(i + 100, n)
        for j in range(i, end):
            window_size = min(max_window, j + 1)
            start = max(0, j - window_size + 1)

            # Fast counting with numpy
            unique_vals, counts = np.unique(states[start:j + 1], return_counts=True)

            # Apply weights (faster than dict-based approach)
            actual_weights = weights[-window_size:]
            weighted_counts = np.zeros_like(unique_vals, dtype=float)

            for k, val in enumerate(unique_vals):
                mask = states[start:j + 1] == val
                weighted_counts[k] = np.sum(actual_weights[mask])

            smoothed[j] = unique_vals[np.argmax(weighted_counts)]

    return smoothed


# Function to compute a dynamic transition matrix from training state sequence
def compute_dynamic_transmat(states, n_components, smoothing, sticky_factor):
    trans_counts = np.zeros((n_components, n_components))
    for prev, curr in zip(states[:-1], states[1:]):
        trans_counts[prev, curr] += 1

    trans_counts += smoothing
    dynamic_transmat = trans_counts / trans_counts.sum(axis=1, keepdims=True)
    sticky_transmat = np.eye(n_components)
    final_transmat = (1 - sticky_factor) * dynamic_transmat + sticky_factor * sticky_transmat

    return final_transmat



def train_gaussianHMM(df, uexpiry, feature_columns, mthsback, forward_bars, 
                      covariance_type="diag", sticky_factor=0.9):
    """
    Train a Gaussian HMM model for regime classification.

    Parameters:
        df (pd.DataFrame): Full DataFrame with required columns
        uexpiry (int): Expiry date to train for
        feature_columns (list): List of column names to use as features
        mthsback (int): Number of months of historical data to use for training
        forward_bars (int): Number of forward bars for regime mapping
        covariance_type (str): Covariance type for HMM. Defaults to "diag"
        sticky_factor (float): Self-transition probability. Defaults to 0.9

    Returns:
        tuple: (trained_model, scaler, regime_mapping, df) or (None, None, None, df) if training fails
    """
    start_time = time.perf_counter()
    try:
        # Find the first instance where fwdper equals 1
        first_instance_date = df.loc[(df['fwdper'] == 1) & (df['expiry'] == uexpiry), 'dt'].iloc[0]

        # Calculate the start date for training (historical only)
        start_date = first_instance_date - pd.DateOffset(months=mthsback)
        
        log.info(f"tghmm -        uexpiry: {uexpiry}, first_instance_date: {first_instance_date}, start_date: {start_date}")
        log.info(f"tghmm -         df len: {len(df)}, first: {df['dt'].iloc[0]}, last: {df['dt'].iloc[-1]}")

        df = df[df['dt'] >= start_date].copy().reset_index(drop=True)
        log.info(f"tghmm - trimmed_df len: {len(df)}, first: {df['dt'].iloc[0]}, last: {df['dt'].iloc[-1]}")
        # Subset the DataFrame for training - ONLY USE HISTORICAL DATA
        train_df = df[(df['dt'] >= start_date) &
                      (df['dt'] < first_instance_date)].reset_index(drop=True)
        log.info(f"tghmm -  trained_df len: {len(train_df)}, first: {train_df['dt'].iloc[0]}, last: {train_df['dt'].iloc[-1]}")

        # Check if we have enough data
        if len(train_df) < 3:
            raise ValueError("Insufficient data for HMM training")

        # Calculate forward returns for n bars during training
        train_df['forward_n_return'] = train_df['return'].rolling(window=forward_bars,
                                                                  min_periods=1).sum().shift(-forward_bars + 1)

        # Drop the last (forward_bars-1) rows where we don't have complete forward returns
        if forward_bars > 1:
            train_df = train_df.iloc[:-forward_bars + 1].copy()

        # Build the feature matrix using the user-provided columns
        X_train = train_df[feature_columns].values
        X_train = np.nan_to_num(X_train, posinf=0, neginf=0)

        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_train)

        # Setup and train model
        n_components = 3
        model = GaussianHMM(n_components=n_components, covariance_type=covariance_type,
                            n_iter=1000, random_state=42)

        # Fit the HMM
        model.fit(X_scaled)

        # Predict the hidden states for training data
        train_states = model.predict(X_scaled)


        # Use the corrected function
        model.transmat_ = compute_dynamic_transmat(train_states, n_components,
                                                   smoothing=1e-1, sticky_factor=sticky_factor)

        # Compute returns from Close to assist in regime mapping
        state_avg_return = {}
        for state in np.unique(train_states):
            state_avg_return[state] = train_df.loc[train_states == state, 'forward_n_return'].mean()

        # Map states to regimes based on average returns
        sorted_states = sorted(state_avg_return, key=lambda s: state_avg_return[s])
        regime_mapping = {
            sorted_states[0]: -1,  # trending down
            sorted_states[-1]: 1,  # trending up
            sorted_states[1]: 0  # mean reverting
        }

        log.info(f"train_gaussianHMM - prod: {df['prd'].unique()[0]}, expiry: {uexpiry}, time: {round(time.perf_counter() - start_time, 4)}")
        return model, scaler, regime_mapping, df

    except Exception as e:
        log.warning(f"Warning: HMM fitting failed with error: {str(e)}")
        return None, None, None, df
    

def add_hmm_regime_fc_fwd(df, uexpiry, feature_columns=None, smoothing_window=5, smoothing_alpha=0.7,
                          covariance_type="diag", mthsback=3, fwdper=1, sticky_factor=0.9,
                          trained_model=None, scaler=None, regime_mapping=None,
                          window_size=50, forward_bars=10):
    """
    HMM regime classification that maintains consistency and eliminates look-ahead bias.
    Uses a fixed model, scaler, and regime mapping for an entire expiry period.
    Predicts states in a way that mimics real-time operation.

    Parameters:
        df (pd.DataFrame): DataFrame with required columns
        feature_columns (list): List of column names to use as features for the HMM
                               If None, defaults to ['hl_diff', 'price_fd']
        smoothing_window (int): Window size for state smoothing
        smoothing_alpha (float): Weight decay factor for smoothing
        sticky (float): Self-transition probability
        covariance_type (str): Covariance type for HMM
        mthsback (int): Number of months back for training
        fwdper (int): Forward period filter
        trained_model, scaler, regime_mapping: Persistent model components
        window_size (int): Maximum number of past points to use for each prediction
        forward_bars (int): Number of forward bars for regime mapping

    Returns:
        updated_df, trained_model, scaler, regime_mapping
    """
    df = df.copy()
    # start_time = time.perf_counter()
    # # Train a new model if one doesn't exist yet
    if trained_model is None:
        trained_model, scaler, regime_mapping, df = train_gaussianHMM(
            df, uexpiry, feature_columns, mthsback, forward_bars, covariance_type, sticky_factor)

    if scaler is None or trained_model is None:
        raise ValueError("No trained model provided")

    # Process data - Simulate real-time prediction
    X = df[feature_columns].values
    X = np.nan_to_num(X, posinf=0, neginf=0)
    X_scaled = scaler.transform(X)

    # print(f"add_hmm_regime_fc_fwd time prod: {df['prd'].unique()[0]}, expiry: {uexpiry} - {round(time.perf_counter() - start_time, 4)}")
    
    # Simulate real-time prediction where each point only sees past data
    states = np.zeros(len(X_scaled), dtype=int)
    for i in range(len(X_scaled)):
        # Use a fixed window of past data for efficiency
        # Adapt the window size based on available data
        curr_window_size = min(window_size, i + 1)  # Use at most window_size past points
        start_idx = max(0, i + 1 - curr_window_size)

        # Use data up to and including the current point
        X_window = X_scaled[start_idx:i + 1]

        # Predict states for this window
        states_window = trained_model.predict(X_window)

        # The last state is the prediction for the current point
        states[i] = states_window[-1]

    # Apply smoothing
    states_smoothed = smooth_states_weighted(states, window=smoothing_window, alpha=smoothing_alpha)

    # Map to regimes using the consistent mapping
    df['hmm_regime'] = pd.Series(states_smoothed).map(regime_mapping).values # type: ignore

    # Filter by fwdper if needed
    df = df[(df['fwdper'] <= fwdper) & (df['expiry']==uexpiry)]

    return df, trained_model, scaler, regime_mapping
    


def add_hmm_regime_fc_fwd2(df, uexpiry, feature_columns=None, smoothing_window=5, smoothing_alpha=0.7,
                          covariance_type="diag", mthsback=3, fwdper=1, sticky_factor=0.9,
                          trained_model=None, scaler=None, regime_mapping=None,
                          window_size=50, forward_bars=10):
    """
    HMM regime classification that maintains consistency and eliminates look-ahead bias.
    Uses a fixed model, scaler, and regime mapping for an entire expiry period.
    Predicts states in a way that mimics real-time operation.

    Parameters:
        df (pd.DataFrame): DataFrame with required columns
        feature_columns (list): List of column names to use as features for the HMM
                               If None, defaults to ['hl_diff', 'price_fd']
        smoothing_window (int): Window size for state smoothing
        smoothing_alpha (float): Weight decay factor for smoothing
        sticky (float): Self-transition probability
        covariance_type (str): Covariance type for HMM
        mthsback (int): Number of months back for training
        fwdper (int): Forward period filter
        trained_model, scaler, regime_mapping: Persistent model components
        window_size (int): Maximum number of past points to use for each prediction
        forward_bars (int): Number of forward bars for regime mapping

    Returns:
        updated_df, trained_model, scaler, regime_mapping
    """
    df = df.copy()
    start_time = time.perf_counter()
    # # Train a new model if one doesn't exist yet
    if trained_model is None:
        trained_model, scaler, regime_mapping, df = train_gaussianHMM(
            df, uexpiry, feature_columns, mthsback, forward_bars, covariance_type, sticky_factor)

    # Process data - Simulate real-time prediction
    X = df[feature_columns].values
    X = np.nan_to_num(X, posinf=0, neginf=0)
    
    if scaler is None or trained_model is None:
        raise ValueError("No trained model provided")
    X_scaled = scaler.transform(X)

    log.info(f"add_hmm_regime_fc_fwd time prod: {df['prd'].unique()[0]}, expiry: {uexpiry} - {round(time.perf_counter() - start_time, 4)}")
    
    # Simulate real-time prediction where each point only sees past data
    states = np.zeros(len(X_scaled), dtype=int)
    for i in range(len(X_scaled)):
        # Use a fixed window of past data for efficiency
        # Adapt the window size based on available data
        curr_window_size = min(window_size, i + 1)  # Use at most window_size past points
        start_idx = max(0, i + 1 - curr_window_size)

        # Use data up to and including the current point
        X_window = X_scaled[start_idx:i + 1]

        # Predict states for this window
        
        states_window = trained_model.predict(X_window)

        # The last state is the prediction for the current point
        states[i] = states_window[-1]

    # Apply smoothing
    states_smoothed = smooth_states_weighted(states, window=smoothing_window, alpha=smoothing_alpha)

    # Map to regimes using the consistent mapping
    df['hmm_regime'] = pd.Series(states_smoothed).map(regime_mapping).values # type: ignore

    # Filter by fwdper if needed
    df = df[(df['fwdper'] <= fwdper) & (df['expiry']==uexpiry)]

    
    return df, trained_model, scaler, regime_mapping