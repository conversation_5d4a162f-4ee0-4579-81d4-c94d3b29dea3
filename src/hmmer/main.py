"""Main module for hmmer."""
import os
import time
import yaml
import logging
import argparse
from typing import Union

import joblib

# from  hmmer.st_mprocess_slowloop import run
# from hmmer.st_mprocess_slowloop import run as run_slow
from hmmer.st_mprocess import run

log = logging.getLogger(__name__)


CORES = os.getenv("CORES")
if CORES is None:
    CORES = os.cpu_count()
else:
    CORES = int(CORES)


PARALLEL_JOBS = CORES

log.info(f"Running with {CORES} cores")

# results = {}

def main(prd: Union[str, None] = None, record_results: bool = True) -> None:
    """Entry point for the application."""
    parser = argparse.ArgumentParser(description="Run the HMMER backtest")
    parser.add_argument("--test", action="store_true", help="run the test version of the hmmer")
    args = parser.parse_args()
    
    prods = []
    log.info("Starting HMMER")
    start_time = time.time()
    if prd is None:
        try:
            with open("./config/config.yaml", "r") as f:
                config = yaml.safe_load(f)
                prods = config["products"]
                # ibarsize = int(config["ibarsize"])
                # starting_year = int(config["starting_year"])
        except Exception as e:
            log.error(f"Error loading products from config: {e}")
    else:
        prods = [prd]
    
    jobs = []
    child_cores = CORES // PARALLEL_JOBS
    for p in prods:
        log.info(f"Running {p}")
        if args.test:
            # if p != 'NG': continue
            # run_test(p, record_results=record_results)
            # run_slow(p, record_results=record_results)
            run(p, record_results=record_results, n_jobs=child_cores)
        else:
            jobs.append(joblib.delayed(run)(p, record_results=record_results, n_jobs=child_cores))
            log.info(f"Finished {p} in {time.time() - start_time:.2f}s")
    
    if args.test:
        pass
    joblib.Parallel(n_jobs=PARALLEL_JOBS, verbose=10)(jobs)
    log.info(f"Total time: {time.time() - start_time:.2f}s")
    
    