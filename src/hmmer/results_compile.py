import os
import glob
import pandas as pd
import numpy as np

path = os.path.join(os.getcwd(), 'summaries')
# Find all CSV files starting with "smrybars" in the given path
csv_files = glob.glob(os.path.join(path, "smrybars*.csv"))
 
multiplier_df = pd.DataFrame({
    'prd': ['CL','NG','HG','GC','NQ','ZS','ZW','ZC','SB','KC'],
    'multiplier': [1000,10000,25000,100,20,50,50,50,1120,375],
    'size':[1,1,1,1,1,1,2,3,3,1]
})

# Dictionary to store DataFrames if needed
dfs = {}

# Process each file
for file in csv_files:
    # Extract filename without extension for dictionary key
    filename = os.path.basename(file).split('.')[0]

    # Read the CSV file
    smrybars = pd.read_csv(file)

    # Calculate cumulative realized PNL
    smrybars['act_realized_pnl'] = smrybars['realized_pnl'].cumsum()

    # Calculate equity curve
    smrybars['equity_curve'] = (smrybars['act_realized_pnl'] + smrybars['unrealized_pnl'])

    smrybars = smrybars.merge(multiplier_df, on='prd', how='left')

    # Multiply equity curve by the corresponding multiplier
    smrybars['equity_curve'] = smrybars['equity_curve'] * smrybars['multiplier'] * smrybars['size']

    # Store the DataFrame in the dictionary
    dfs[filename] = smrybars




import plotly.graph_objects as go
import plotly.io as pio
pio.renderers.default = "browser"  # Opens in default browser

# Create a list to store processed Series for each product
processed_series = []

# Process each dataframe
for key, df in dfs.items():
    # Extract product name from the key (assuming format like 'smrybars_ZC')
    product = key.split('_')[1] if '_' in key else key

    # Check which equity curve column to use
    equity_col = 'equity_curve_adjusted' if 'equity_curve_adjusted' in df.columns else 'equity_curve'

    # Ensure dt is datetime type with proper handling of timezone information
    if not pd.api.types.is_datetime64_any_dtype(df['dt']):
        df['dt'] = pd.to_datetime(df['dt'], utc=True)

    # Create a copy with just the datetime and equity column
    temp_df = df[['dt', equity_col]].copy()

    # Set datetime as index
    temp_df = temp_df.set_index('dt')

    # Handle duplicate indices by keeping the last value
    temp_df = temp_df[~temp_df.index.duplicated(keep='last')]

    # Rename the column to the product name
    temp_df.columns = [product]

    # Add to our list of processed series
    processed_series.append(temp_df)

# Concatenate all the series into a single dataframe
combined_df = pd.concat(processed_series, axis=1)

# Sort index to ensure chronological order
combined_df = combined_df.sort_index()

# Forward fill NaN values (carry forward the last known equity value)
combined_df = combined_df.fillna(method='ffill')

# Fill any remaining NaNs with zeros (for the beginning periods)
combined_df = combined_df.fillna(0)

# Calculate the portfolio equity curve (sum across all products)
combined_df['Portfolio'] = combined_df.sum(axis=1)


# Create the plot
fig = go.Figure()

# Add portfolio trace first (will be most visible)
fig.add_trace(go.Scatter(
    x=combined_df.index,
    y=combined_df['Portfolio'],
    mode='lines',
    name='Portfolio',
    line=dict(color='black', width=3)
))

# Add individual product traces
colors = ['blue', 'red', 'green', 'purple', 'orange']
for i, product in enumerate([col for col in combined_df.columns if col != 'Portfolio']):
    color = colors[i % len(colors)]
    fig.add_trace(go.Scatter(
        x=combined_df.index,
        y=combined_df[product],
        mode='lines',
        name=f'{product}',
        line=dict(color=color, width=1.5),
        opacity=0.7
    ))

# Update layout
fig.update_layout(
    title='Portfolio Performance',
    xaxis_title='Date',
    yaxis_title='Portfolio Value ($)',
    template='plotly_white',
    legend=dict(x=0.01, y=0.99, bordercolor="Black", borderwidth=1)
)

# Show the plot
fig.show()


''' Performance Dataframe Below

'''
# First, let's resample to monthly data to calculate monthly returns
monthly_df = combined_df.resample('M').last()

# Calculate monthly returns (difference between consecutive months)
monthly_returns = monthly_df.diff()

# Create a summary dataframe by year
yearly_summary = pd.DataFrame()

# Get all years in the data
years = pd.DatetimeIndex(monthly_returns.index).year.unique()

for year in years:
    # Filter data for the current year
    year_data = monthly_returns[monthly_returns.index.year == year]

    # Portfolio monthly returns for current year
    portfolio_returns = year_data['Portfolio']

    # Calculate metrics for the portfolio
    winning_months = (portfolio_returns > 0).mean() * 100
    total_pnl = monthly_df.loc[monthly_df.index.year == year, 'Portfolio'].iloc[-1] - \
                monthly_df.loc[monthly_df.index.year == year, 'Portfolio'].iloc[0]
    monthly_pnl_sd = portfolio_returns.std()
    sharpe_ratio = (portfolio_returns.mean() / monthly_pnl_sd) * np.sqrt(12) if monthly_pnl_sd != 0 else 0
    monthly_max_win = portfolio_returns.max()
    monthly_max_loss = portfolio_returns.min()

    # Calculate max drawdown
    portfolio_equity = monthly_df.loc[monthly_df.index.year == year, 'Portfolio']
    rolling_max = portfolio_equity.cummax()
    drawdown = (portfolio_equity - rolling_max) / rolling_max
    max_drawdown = drawdown.min() * 100  # Convert to percentage

    # Find best and worst products for the year
    product_columns = [col for col in year_data.columns if col != 'Portfolio']

    if product_columns:
        annual_product_returns = {}
        for product in product_columns:
            start_value = monthly_df.loc[monthly_df.index.year == year, product].iloc[0]
            end_value = monthly_df.loc[monthly_df.index.year == year, product].iloc[-1]
            annual_product_returns[product] = end_value - start_value

        best_product = max(annual_product_returns.items(), key=lambda x: x[1])[0]
        worst_product = min(annual_product_returns.items(), key=lambda x: x[1])[0]
    else:
        best_product = "N/A"
        worst_product = "N/A"

    # Add to summary dataframe
    yearly_summary.loc[year, 'Winning Month %'] = winning_months
    yearly_summary.loc[year, 'Total PnL'] = total_pnl
    yearly_summary.loc[year, 'Monthly PnL SD'] = monthly_pnl_sd
    yearly_summary.loc[year, 'Sharpe Ratio'] = sharpe_ratio
    yearly_summary.loc[year, 'Monthly Max Win'] = monthly_max_win
    yearly_summary.loc[year, 'Monthly Max Loss'] = monthly_max_loss
    yearly_summary.loc[year, 'Max Drawdown %'] = max_drawdown
    yearly_summary.loc[year, 'Best Product'] = best_product
    yearly_summary.loc[year, 'Worst Product'] = worst_product

# Format the numeric columns
cols_to_round = [
    'Winning Month %',
    'Total PnL',
    'Monthly PnL SD',
    'Sharpe Ratio',
    'Monthly Max Win',
    'Monthly Max Loss',
    'Max Drawdown %'
]

yearly_summary[cols_to_round] = np.round(yearly_summary[cols_to_round], 2)
yearly_summary = yearly_summary[yearly_summary.index >= 2018].copy()
#%.2f



