import pandas as pd
import numpy as np
pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 500)
pd.set_option('display.width', 1000)

#Helper function for add expiry position
def calculate_expiry_dates(date_range, change_day, exit_mths, num_columns):
    # Extend the overall range: one year before and 20 years after.
    start_ext = date_range[0] - pd.DateOffset(years=1)
    end_ext = date_range[-1] + pd.DateOffset(years=20)
    extended_range = pd.date_range(start_ext, end_ext, freq='D')

    # Floor each date to the first day of its month.
    first_of_month = extended_range.to_period('M').to_timestamp()

    # Add the change_day to get candidate expiry dates.
    candidates = first_of_month + pd.to_timedelta(change_day, unit='D')

    # Filter candidates whose month is in exit_mths.
    candidates = candidates[candidates.month.isin(exit_mths)]
    candidates = candidates.drop_duplicates()

    # Convert to a NumPy array.
    candidates_np = candidates.to_numpy()

    # Get the trade dates as a NumPy array.
    trade_dates_np = date_range.to_numpy()

    # For each trade date, find the first candidate that is >= the trade date.
    indices = np.searchsorted(candidates_np, trade_dates_np)

    # Reshape for broadcasting
    row_indices = indices.reshape(-1, 1)
    col_indices = np.arange(num_columns).reshape(1, -1)
    combined_indices = row_indices + col_indices

    # Use the combined indices to pick out the forward candidates.
    forward_candidates = candidates_np[combined_indices]

    # Subtract the change_day to obtain the contract month.
    forward_candidates = forward_candidates - pd.to_timedelta(change_day, unit='D')

    # Create the DataFrame.
    df_forward = pd.DataFrame(
        forward_candidates,
        columns=[f'V{i + 1}' for i in range(num_columns)]
    )
    # Add the trade date column
    df_forward.insert(0, 'Date', date_range)
    return df_forward


def add_expiry_position(
    df,
    products_with_change_days,
    trade_date_col='Date',
    product_col='Product',
    trade_year_col='exp_yr',  # used for the contract code (e.g. "2025")
    trade_month_col='exp_mnum',  # used for the contract month number (e.g. "12")
):
    """
    Add a forward period indicator column ("fwdper") to the dataframe by matching each
    trade’s contract code with a pre‐computed lookup table of forward expiry dates.

    Parameters:
      df: pandas DataFrame containing trade/price data.
      products_with_change_days: list of tuples, for example:
           [('GC', -5), ('HG', 10)]
         where each tuple is (product, change_day).
      trade_date_col: name of the column holding the trade date (will be normalized to remove any time)
      product_col: name of the column holding the product code.
      trade_year_col: name of the column holding the expiry “year” (used to create the contract code)
      trade_month_col: name of the column holding the expiry “month” number (used to create the contract code)

    The function assumes that the DataFrame has the necessary columns to form a contract code
    as:  exp_yr_m = <trade_year> + '_' + <trade_month>

    The returned DataFrame is a (subset of the original) with a new column "fwdper".
    """
    # Work on a copy so that the original is not modified.
    df = df.copy()

    # Ensure the trade date is in datetime format and “normalized” (i.e. no time component).
    df[trade_date_col] = pd.to_datetime(df[trade_date_col]).dt.normalize()

    # Filter the DataFrame for only the products of interest.
    # products_with_change_days is assumed to be a list of (product, change_day) tuples.
    products = [prod for prod, _ in products_with_change_days]
    df_filtered = df[df[product_col].isin(products)].copy()

    # Create the contract code column ("exp_yr_m") by concatenating the year and month fields.
    df_filtered['exp_yr_m'] = pd.to_datetime(
        df_filtered[trade_year_col].astype(int).astype(str) + '-' +
        df_filtered[trade_month_col].astype(int).astype(str).str.zfill(2) + '-01'
    )

    # Get the overall trade date range from the filtered data.
    start_date = df_filtered[trade_date_col].min()
    end_date = df_filtered[trade_date_col].max()
    date_range = pd.date_range(start_date, end_date, freq='D')

    num_columns = 20  # number of forward expiry dates to compute

    result_list = []  # container for results (one block per product)

    # Loop through each product/change_day pair
    #prod, change_day = products_with_change_days[0]
    for prod, change_day in products_with_change_days:
        # Subset the filtered DataFrame for the current product.
        prod_df = df_filtered[df_filtered[product_col] == prod].copy()
        if prod_df.empty:
            continue  # nothing to do if no rows

        # Get the unique expiry month numbers seen for this product.
        expiry_months = np.sort(prod_df[trade_month_col].unique())
        # For each expiry month, compute the “exit month.”
        # (We use an arbitrary year — e.g. 2000 — since only the month matters.)
        exit_mths = sorted({
            (pd.Timestamp(year=2000, month=int(m), day=1) + pd.Timedelta(days=change_day)).month
            for m in expiry_months
        })

        # Calculate the forward expiry dates (as a lookup table) for all trade dates.
        expiry_df = calculate_expiry_dates(date_range, change_day, exit_mths, num_columns)

        # Reshape the lookup table to long format.
        expiry_long = expiry_df.melt(id_vars='Date', var_name='fwdper', value_name='exp_yr_m')
        # Our columns were named "V1", "V2", … so strip the "V" and convert to integer.
        expiry_long['fwdper'] = expiry_long['fwdper'].str.replace('V', '').astype(int)

        # Rename the lookup table’s trade date column to match the DataFrame.
        expiry_long = expiry_long.rename(columns={'Date': trade_date_col})

        # Merge the product’s trades with the lookup table on trade date and contract code.
        merged = pd.merge(prod_df, expiry_long, on=[trade_date_col,'exp_yr_m'], how='left')
        # Keep only rows that found a match (i.e. where a forward period could be determined).
        merged = merged[~merged['fwdper'].isna()].copy()
        merged['fwdper'] = merged['fwdper'].astype(int)

        result_list.append(merged)

    # Concatenate the results from all products.
    if result_list:
        result_df = pd.concat(result_list, ignore_index=True)
    else:
        result_df = pd.DataFrame()
    result_df = result_df.drop('exp_yr_m', axis=1)

    return result_df


def create_continuous_futures(prdf):
    # Filter for relevant forward periods and reset index
    prdf1 = prdf[prdf['fwdper'].isin([1, 2])].copy().reset_index(drop=True)

    # Calculate days to expiry for each group
    prdf1['daystolast'] = prdf1.groupby('expiry')['date'].transform(lambda x: (x.max() - x).dt.days + 1)

    # Pivot prdf1 to wide format
    prdf_wide = prdf1.pivot(index='date', columns='fwdper', values=['Close', 'daystolast']).reset_index()

    # Flatten the multi-index columns
    prdf_wide.columns = [f'{j}_{i}' if j else i for i, j in prdf_wide.columns]
    prdf_wide = prdf_wide.rename(columns={'1_Close': 'Close_1', '2_Close': 'Close_2',
                                          '1_daystolast': 'daystolast_1', '2_daystolast': 'daystolast_2'})

    # Fill down missing values
    prdf_wide.fillna(method='ffill', inplace=True)

    # Identify roll dates
    roll_dates = prdf_wide[prdf_wide['daystolast_1'] == 1][['date', 'Close_1', 'Close_2']]
    roll_dates = roll_dates.iloc[:-1].copy() # Exclude the most recent roll date

    # Calculate the adjustment ratios on roll dates
    roll_dates['ratio'] = roll_dates['Close_2'] / roll_dates['Close_1']

    # Initialize the continuous futures series
    prdf_wide['continuous_futures'] = prdf_wide['Close_1']

    # Adjust the historical prices by the ratios
    for i in range(len(roll_dates)):
        roll_date = roll_dates.iloc[i]['date']
        adjustment_ratio = roll_dates.iloc[i]['ratio']
        prdf_wide.loc[prdf_wide['date'] <= roll_date, 'continuous_futures'] *= adjustment_ratio

    return prdf_wide
