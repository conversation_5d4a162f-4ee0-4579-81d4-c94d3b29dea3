__all__ = [
    'get_parameters', 
    'get_parameter', 
    'get_instdefs', 
    # 'get_bars', 
    'load_hmmdb_data'
]
import os
import logging
import time as ttime
from datetime import datetime, time
from dateutil.relativedelta import relativedelta
from zoneinfo import ZoneInfo

import pandas as pd

import tradertools as tt

from hmmer._parameters import Parameter

PG_CLIENT = os.getenv("POSTGRES_CLIENT")

log = logging.getLogger(__name__)


def get_parameters() -> pd.DataFrame:
    """
    Get parameters from the database.

    Returns:
        pd.DataFrame: DataFrame containing the parameters.
    """
    parameters = dict[str, Parameter]()
    
    pg = tt.clients.get_pg_client(PG_CLIENT)
    res = pg.query("SELECT * FROM hmmer.parameters")
    for row in res:
        del row['created_at']
        del row['updated_at']
        parameters[row['product']] = Parameter(**row)
    df = pd.DataFrame(list(parameters.values()))
    
    return df


def get_parameter(product: str) -> Parameter:
    """
    Get parameters from the database.

    Args:
        product (str): Product name.

    Returns:
        Parameter: Parameter object.
    """
    pg = tt.clients.get_pg_client(PG_CLIENT)
    res = pg.query(f"SELECT * FROM hmmer.parameters WHERE product = '{product}'")
    if len(res) == 0:
        raise ValueError(f"Product {product} not found in database")
    del res[0]['created_at']
    del res[0]['updated_at']
    
    # return Parameter(**res[0])
    return Parameter.from_db_row(res[0])


# def get_instdefs(product: str, starting_year: int) -> list[str]:
#     """
#     Get instrument definitions from the database, filtering to only include
#     contracts from starting_year up to the front month contract.

#     Args:
#         product (str): Product name.
#         starting_year (int): The earliest year to include.

#     Returns:
#         list[str]: List of instrument definition names (name2).
#     """
#     instdefs = tt.instruments.get_definitions()
    
#     # Get all valid instruments for this product from the starting year
#     valid_instdefs = [i for i in instdefs if i.name2 and i.year and 
#                       i.year >= starting_year and i.product == product]
    
#     # Sort by expiry date to find the front month
#     valid_instdefs.sort(key=lambda x: x.expiry if x.expiry else datetime.max.date())
    
#     # Find the front month contract (first one that hasn't expired yet)
#     today = datetime.now().date()
#     front_month = None
#     for inst in valid_instdefs:
#         if inst.expiry and inst.expiry >= today:
#             front_month = inst
#             break
    
#     # If no front month found, return all valid instruments
#     if not front_month:
#         return [i.name2 for i in valid_instdefs if i.name2]
    
#     # Filter to only include instruments up to the front month
#     filtered_instdefs = [i for i in valid_instdefs if i.expiry and 
#                          i.expiry <= (front_month.expiry + timedelta(days=30))] # type: ignore
    
#     ret = [i.name2 for i in filtered_instdefs if i.name2]
#     print(ret)
#     return ret


def get_instdefs(product: str, months_back: int) -> list[str]:
    """
    Get instrument definitions from the database.

    Returns:
        list[InstrumentDefinition]: List of instrument definitions.
    """
    instdefs = tt.instruments.get_definitions()
    p = tt.instruments.get_product(product)
    mths = p.tradeable_months
    starting_contract = datetime.now().date() - relativedelta(months=months_back) 
    ret = []
    for i in instdefs:
        # if (
        #     i.name2 and 
        #     i.year and 
        #     i.year >= starting_year and 
        #     i.product == product and
        #     i.monthCode in mths):
        #     ret.append(i.name2)
        if (
            i.name2 and 
            i.year and 
            i.product == product and
            i.monthCode in mths and
            i.expiry and
            i.contractMonth and
            i.contractMonth >= starting_contract):
            ret.append(i.name2)
    # ret = [i.name2 for i in instdefs if i.name2 and i.year and i.year >= starting_year and i.product == product]
    return ret



# def get_bars(prod: str, name2s: list[str], bar_interval: int) -> pd.DataFrame:
#     """
#     Get bars from the database.

#     Args:
#         prod (str): Product name.

#     Returns:
#         pd.DataFrame: DataFrame containing the bars.
#     """
#     pg = tt.clients.get_pg_client(PG_CLIENT)
#     table_name = f"{prod.upper()}_{bar_interval}min"
#     name2s = [f"'{n}'" for n in name2s]
#     query = f"SELECT * FROM hmmer_bars.\"{table_name}\" "
#     query += f"WHERE name2 IN ({', '.join(name2s)})"
#     log.info(f'Downloading {prod} - {query}...')
    
#     start = time.perf_counter()
#     df = pd.DataFrame(pg.query(query))
#     log.info(f'Downloaded in {time.perf_counter() - start:.2f}s')
#     df['product'] = prod
    
#     # TODO - refactor code to use db names
#     columns_to_capitalize = ['close', 'open', 'high', 'low', 'volume']
#     df.columns = [col.capitalize() if col.lower() in columns_to_capitalize else col for col in df.columns]

#     # Set dt to datetime and convert to America/New_York timezone
#     df['dt'] = pd.to_datetime(df['dt'])
#     df['dt'] = df['dt'].dt.tz_convert('America/New_York')
    
#     # Add expiry columns
#     df['expiry_mth'] = df['expiry'].astype(int).astype(str).str[-2:].astype(int)
#     df['expiry_yr'] = df['expiry'].astype(int).astype(str).str[:4].astype(int)
#     df['TradeDate'] = pd.to_datetime(df['dt']).dt.date
    
#     return df






#Function to pull DTN data from hmm database
def load_hmmdb_data(product: str, runtimestamp: datetime, shifttime: time, ct_mnths_back: int = 18, months_back: int = 10):
    start_time = ttime.perf_counter()
    pg = tt.clients.get_pg_client(PG_CLIENT)
    # # Define CYCLES for month code filtering
    # CYCLES = {
    #     "ZC": "H K N U Z".split(),          # Corn
    #     "ZW": "H K N U Z".split(),          # Wheat
    #     "HG": "H K N U Z".split(),          # Copper
    #     "NG": list("FGHJKMNQUVXZ"),         # NatGas – all 12 months
    #     "CL": list("FGHJKMNQUVXZ"),         # Crude – all 12 months
    #     "SB": "H K N V".split(),            # Sugar
    #     "GC": "G J M Q V Z".split(),        # Gold
    #     "ZS": "F H K N Q U X".split(),      # Soybeans
    #     "NQ": "H M U Z".split(),            # E-mini Nasdaq
    #     "KC": "H K N U Z".split(),          # Coffee
    # }

    # # --- build engine from YAML ------------------------------------------------
    # db = yaml.safe_load(Path("connections.yaml").read_text())["connections"][PG_CLIENT]
    # url = (f"postgresql+psycopg://{db['user']}:{db['password']}@{db['host']}:{db['port']}/{db['database']}"
    #        + ("?sslmode=require" if db.get("ssl") else ""))
    # engine = create_engine(url)

    # # --- query last 10 years ---------------------------------------------------
    # starting_year = datetime.now().year - years_back
    name2s = get_instdefs(product, months_back)
    name2s = [f"'{n}'" for n in name2s]
    
    if runtimestamp.tzinfo is None:
        runtimestamp = runtimestamp.replace(tzinfo=ZoneInfo("America/New_York"))
        
    
    # query = f'''
    #     SELECT *
    #     FROM hmmer_bars."{product}_30min"
    #     WHERE name2 IN ({', '.join(name2s)})
    # '''
    
    # query = f'''
    # WITH max_dt AS ( 
    #     SELECT name2, MAX(dt) AS max_dt 
    #     FROM hmmer_bars."{product}_30min" 
    #     GROUP BY name2 
    # ) 
    # SELECT c.* 
    # FROM hmmer_bars."{product}_30min" c 
    # JOIN max_dt m ON c.name2 = m.name2 
    # WHERE c.dt >= m.max_dt - INTERVAL '{ct_mnths_back} months' 
    # ORDER BY c.name2, c.dt; 
    # '''
    
    # query = f'''
    # SELECT *
    # FROM (
    # SELECT *,
    #     MAX(dt) OVER (PARTITION BY name2) as max_dt_for_name
    # FROM hmmer_bars."{product}_30min"
    # ) ranked
    # WHERE dt >= max_dt_for_name - INTERVAL '{ct_mnths_back} months'
    # ORDER BY name2, dt;
    # '''
    
    query = f'''
    SELECT dt, name2, expiry, "open", high, low, "close", volume
    FROM (
        SELECT *,
            MAX(dt) OVER (PARTITION BY name2) as max_dt_for_name
        FROM hmmer_bars."{product}_30min"
        WHERE name2 IN ({', '.join(name2s)})
    ) ranked
    WHERE dt >= max_dt_for_name - INTERVAL '{ct_mnths_back} months'
    AND dt <= '{runtimestamp}'
    ORDER BY name2, dt;
    '''
    
    # query = f'''
    #     SELECT *
    #     FROM hmmer_bars."{product}_30min"
    #     WHERE dt >= (CURRENT_DATE - INTERVAL '10 years')
    # '''

    # sql = text(query)
    # with engine.connect() as conn:
    #     idata = pd.read_sql_query(sql, conn)
    
    idata = pd.DataFrame(pg.query(query))
    query_time = round(ttime.perf_counter() - start_time, 2)
    log.info(f"{product} - Query time: {query_time} seconds")

    # --- tidy up ---------------------------------------------------------------
    # mc = {'F':1,'G':2,'H':3,'J':4,'K':5,'M':6,'N':7,'Q':8,'U':9,'V':10,'X':11,'Z':12}
    # idata['prd']        = idata['name2'].str[:2]
    # idata['month_code'] = idata['name2'].str[2]
    # idata['year_code']  = idata['name2'].str[-2:]
    # idata['expiry_mth'] = idata['month_code'].map(mc)
    # idata['expiry_yr']  = 2000 + idata['year_code'].astype(int)
    # idata['expiry']     = idata['expiry_yr']*100 + idata['expiry_mth']

    
    # idata['dt'] = pd.to_datetime(idata['dt'])
    # idata['dt'] = idata['dt'].dt.tz_convert('America/New_York')

    # idata['expiry_mth'] = idata['expiry'].astype(int).astype(str).str[-2:].astype(int)
    # idata['expiry_yr'] = idata['expiry'].astype(int).astype(str).str[:4].astype(int)
    # idata['expiry'] = idata['expiry'].astype(int)
    # idata['TradeDate'] = pd.to_datetime(idata['dt']).dt.date

    # # result = (idata.rename(columns={'dt':'dt','open':'Open','high':'High','low':'Low',
    # #                                 'close':'Close','volume':'Volume'})
    # #                 [['dt','Open','High','Low','Close','Volume','prd','expiry','expiry_mth','expiry_yr', 'TradeDate']])

    # result = idata.rename(columns={'dt':'dt','open':'Open','high':'High','low':'Low',
    #                                 'close':'Close','volume':'Volume'})
    
    # cols = ['dt', 'TradeDate', 'prd', 'name2', 'expiry', 'expiry_mth', 'expiry_yr', 'Open', 'High', 'Low', 'Close', 'Volume']
    # result = result[cols]
    
    # result['dt'] = pd.to_datetime(result['dt'], utc=True).dt.tz_convert('America/New_York')
    
    # result['TradeDate'] = adjusted_tradedate(result, shifttime)
        # Convert dt once and set timezone
    idata['dt'] = pd.to_datetime(idata['dt'], utc=True).dt.tz_convert('America/New_York')
    
    # Parse instrument codes once
    mc = {'F':1,'G':2,'H':3,'J':4,'K':5,'M':6,'N':7,'Q':8,'U':9,'V':10,'X':11,'Z':12}
    idata['prd'] = idata['name2'].str[:2]
    idata['month_code'] = idata['name2'].str[2]
    idata['year_code'] = idata['name2'].str[-2:]
    idata['expiry_mth'] = idata['month_code'].map(mc)
    idata['expiry_yr'] = 2000 + idata['year_code'].astype(int)
    idata['expiry'] = (idata['expiry_yr'] * 100 + idata['expiry_mth']).astype(int)
    
    # Set initial TradeDate
    idata['TradeDate'] = idata['dt'].dt.date
    
    # Rename columns and select final columns
    result = idata.rename(columns={'open':'Open','high':'High','low':'Low','close':'Close','volume':'Volume'})
    cols = ['dt', 'TradeDate', 'prd', 'name2', 'expiry', 'expiry_mth', 'expiry_yr', 'Open', 'High', 'Low', 'Close', 'Volume']
    result = result[cols]
    
    # Apply TradeDate adjustment
    result['TradeDate'] = adjusted_tradedate(result, shifttime)
    
    duration = round(ttime.perf_counter() - start_time, 2)
    log.info(f"{product} - Download time: {duration} seconds")
    return result.sort_values('dt').reset_index(drop=True)



def adjusted_tradedate(df: pd.DataFrame, shifttime: time) -> pd.Series:
    df = df.copy()
    # Create a mapping of each business day to the next available day
    uniq = sorted(df['TradeDate'].unique())
    next_day = {d: uniq[i+1] if i < len(uniq)-1 else d for i, d in enumerate(uniq)}
    # For times after shifttime, map the TradeDate to the next available business day
    mask = df['dt'].dt.time > shifttime
    df.loc[mask, 'TradeDate'] = df.loc[mask, 'TradeDate'].map(next_day)
    
    return df['TradeDate']
