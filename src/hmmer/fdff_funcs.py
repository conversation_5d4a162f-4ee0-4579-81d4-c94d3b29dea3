
import logging
import numpy as np
import pandas as pd
from scipy.fft import next_fast_len
from numba import jit
import scipy.signal

log = logging.getLogger("fdiff_benchmark") 


# Original functions for comparison
def diffseries_original(x, d):
    """Original diffseries function"""
    iT = len(x)
    if iT < 2:
        raise ValueError("Length of x must be at least 2")
    x = x - np.mean(x)
    np2 = next_fast_len(iT + iT - 1)
    pad = np.zeros(np2 - iT) # type: ignore
    k = np.arange(1, iT)
    b = np.concatenate(([1], np.cumprod((k - (d + 1)) / k), pad))
    dx = np.fft.ifft(np.fft.fft(b) * np.fft.fft(np.concatenate((x, pad))))[:iT]
    return np.real(dx)

def rolling_fractional_differencing_original(group: pd.DataFrame, column, window, d):
    """Original rolling fractional differencing function"""
    group = group.sort_index()
    result = np.full(group.shape[0], np.nan)
    for i in range(len(group), window - 1, -1):
        result[i - 1] = diffseries_original(group[column].iloc[i - window:i].values, d)[-1]
    return pd.Series(result, index=group.index)



# Optimized version 1: Pre-compute coefficients and use scipy.signal
def compute_fractional_diff_coefficients(max_window, d):
    """Pre-compute fractional differencing coefficients"""
    k = np.arange(1, max_window)
    coeffs = np.concatenate(([1], np.cumprod((k - (d + 1)) / k)))
    return coeffs

def rolling_fractional_differencing_v1(group: pd.DataFrame, column, window, d):
    """Optimized version using pre-computed coefficients"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute coefficients once
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # Extract window data and center it
        window_data = data[i - window + 1:i + 1]
        centered_data = window_data - np.mean(window_data)
        
        # Apply fractional differencing using convolution
        # Reverse coefficients for proper convolution
        diff_result = np.convolve(centered_data, coeffs[:window], mode='valid')
        result[i] = diff_result[-1] if len(diff_result) > 0 else centered_data[-1]
    
    return pd.Series(result, index=group.index)

# Optimized version 2: Numba JIT compiled version
@jit(nopython=True)
def fractional_diff_numba(data, coeffs, window):
    """Numba-compiled fractional differencing for a single window"""
    n = len(data)
    centered = data - np.mean(data)
    result = 0.0
    
    for j in range(min(window, len(coeffs))):
        if j < n:
            result += coeffs[j] * centered[n - 1 - j]
    
    return result

def rolling_fractional_differencing_v2(group: pd.DataFrame, column, window, d):
    """Numba-optimized version"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute coefficients
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_data = data[i - window + 1:i + 1]
        result[i] = fractional_diff_numba(window_data, coeffs, window)
    
    return pd.Series(result, index=group.index)

# Optimized version 3: Vectorized approach with scipy.signal
def rolling_fractional_differencing_v3(group: pd.DataFrame, column, window, d):
    """Vectorized approach using scipy.signal.lfilter"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute coefficients
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result = np.full(n, np.nan)
    
    # Use scipy's lfilter for efficiency
    for i in range(window - 1, n):
        window_data = data[i - window + 1:i + 1]
        centered_data = window_data - np.mean(window_data)
        
        # Apply filter (note: lfilter applies coefficients in forward direction)
        filtered = scipy.signal.lfilter(coeffs[:window], [1], centered_data)
        result[i] = filtered[-1]
    
    return pd.Series(result, index=group.index)

# Most optimized version 4: Minimize redundant calculations
@jit(nopython=True)
def rolling_fractional_diff_core(data, coeffs, window):
    """Core computation with minimal overhead"""
    n = len(data)
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # Extract window
        start_idx = i - window + 1
        # window_sum = 0.0
        
        # Compute mean
        mean_val = 0.0
        for j in range(window):
            mean_val += data[start_idx + j]
        mean_val /= window
        
        result[i] = 0.0
        
        # Apply fractional differencing
        for j in range(window):
            if j < len(coeffs):
                result[i] += coeffs[j] * (data[start_idx + window - 1 - j] - mean_val)
        
    return result

def rolling_fractional_differencing_v4(group: pd.DataFrame, column, window, d):
    """Most optimized version with numba"""
    group = group.sort_index()
    data = group[column].values
    
    # Pre-compute coefficients
    coeffs = compute_fractional_diff_coefficients(window, d)
    
    result_array = rolling_fractional_diff_core(data, coeffs, window)
    
    return pd.Series(result_array, index=group.index)

# Alternative: FFT-based approach with better memory management
def rolling_fractional_differencing_fft_optimized(group: pd.DataFrame, column, window, d):
    """FFT-optimized version that reuses computations"""
    group = group.sort_index()
    data = group[column].values
    n = len(data)
    
    # Pre-compute FFT size and coefficients
    np2 = next_fast_len(window + window - 1)
    k = np.arange(1, window)
    b_coeffs = np.concatenate(([1], np.cumprod((k - (d + 1)) / k)))
    
    # Pad coefficients for FFT
    b_padded = np.concatenate((b_coeffs, np.zeros(np2 - window)))
    b_fft = np.fft.fft(b_padded)  # Pre-compute FFT of coefficients
    
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_data = data[i - window + 1:i + 1]
        centered_data = window_data - np.mean(window_data)
        
        # Pad data for FFT
        data_padded = np.concatenate((centered_data, np.zeros(np2 - window)))
        data_fft = np.fft.fft(data_padded)
        
        # Convolution via FFT
        conv_result = np.fft.ifft(b_fft * data_fft)[:window]
        result[i] = np.real(conv_result[-1])
    
    return pd.Series(result, index=group.index)


funcs = {
    'original': rolling_fractional_differencing_original,
    'v1': rolling_fractional_differencing_v1,
    'v2': rolling_fractional_differencing_v2,
    'v3': rolling_fractional_differencing_v3,
    'v4': rolling_fractional_differencing_v4,
    'fft': rolling_fractional_differencing_fft_optimized
}
