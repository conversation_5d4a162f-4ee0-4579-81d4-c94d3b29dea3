__all__ = [
    "IBClient"
]
import os
import sys
import threading
import inspect
import time
import logging
from typing import Dict, List, Tuple, Any, Optional, Callable
from datetime import datetime
from decimal import Decimal



from ibapi.client import EClient
# from ibapi.common import OrderId
from ibapi.wrapper import EWrapper

from ibapi.contract import Contract, ContractDetails
from ibapi.order import Order
from ibapi.order_cancel import OrderCancel
from ibapi.tag_value import TagValue
from ibapi.order_state import OrderState
from ibapi.execution import Execution, ExecutionFilter
from ibapi.commission_and_fees_report import CommissionAndFeesReport

from .._fill import Fill
from executor.types.enums import Side
from executor.types.constants import TZ

log = logging.getLogger(__name__)

MAX_RECONNECT_ATTEMPTS = 5

ALLOW_TRADING = os.getenv("ALLOW_TRADING", "False").lower() == "true"



class IBClient(EClient, EWrapper):
    def __init__(self, host: str, port: int, clientId: int):
        EClient.__init__(self, self)
        
        self._host: str = host
        self._port: int = port
        self._clientId: int = clientId
        
        self.reconnect_attempts = 0
        self._initialized = False
        self._shutdown_requested = False
        
        self.nextValidOrderId = -1
        
        self.contracts: Dict[int, ContractDetails] = {} # conId to ContractDetails
        self.openOrders: Dict[int, Order] = {} # permId to Order
        """
        openOrders is a dictionary of permId to Order. 
        """
        
        self.executions: Dict[str, Execution] = {} # execId to Execution
        self.commission_reports: Dict[str, CommissionAndFeesReport] = {} # execId to CommissionAndFeesReport
        
        self.permId2conId: Dict[int, int] = {} # orderId to conId
        
        # Mapping of conId to name2
        self._contract_details_requests: Dict[int, str] = {}
        
        # Events for blocking requests
        self._contract_details_complete = threading.Event()
        self._open_orders_complete = threading.Event()
        self._executions_complete = threading.Event()

        self._onOrder_callbacks: List[Callable] = []
        self._onFill_callbacks: List[Callable] = []
        
        log.info(f"ALLOW_TRADING: {ALLOW_TRADING}")
        log.info("Created IBClient")


    def connect(self, host: str, port: int, clientId: int):
        self._host = host
        self._port = port
        self._clientId = clientId
        super().connect(host, port, clientId)
        log.info(f"Connected to IB: {host}:{port} - clientId:{clientId}")

    def disconnect(self):
        super().disconnect()
        
        log.info("Disconnected from IB")
        
    def shutdown(self):
        log.info("Shutdown requested")
        self._shutdown_requested = True
        self.disconnect()
        

    def check_and_reconnect(self):
        """Check connection health and reconnect if needed"""
        if self._shutdown_requested:
            return
        
        if not self.isConnected():
            log.info("attempting reconnect...")
            self.reconnect_attempts += 1
            if self.reconnect_attempts > MAX_RECONNECT_ATTEMPTS:
                log.error("Max reconnect attempts reached. Exiting.")
                sys.exit(1)
            try:
                self.disconnect()
                time.sleep(5)
                
                if self._host is None or self._port is None or self._clientId is None:
                    log.error("Cannot reconnect: missing connection parameters")
                    return
                    
                self.connect(self._host, self._port, self._clientId)
                
            except Exception as e:
                log.error(f"Reconnection failed: {e}")
        
        
        
        

    ##### Order Management #####
    def placeMarketOrder(self, conId: int, side: Side, qty: float, price: Optional[float],  orderRef: Optional[str] = None, tif: str = "GTC"): # type: ignore
        
        contract = self.contracts[conId].contract

        order = Order()
        order.orderType = "MKT"
        order.action = side.value
        order.totalQuantity = Decimal(qty)
        order.tif = tif
        order.conditionsIgnoreRth = True
        order.outsideRth = True
        
        if orderRef is not None:
            order.orderRef = orderRef
        
        orderId = self._placeOrder(contract, order)
        return orderId
        
        
    def placeAdaptiveMarket(self, conId: int, side: Side, qty: float, orderRef: Optional[str] = None, tif: str="GTC") -> Tuple[int, str]: 
        
        contract = self.contracts[conId].contract
        
        order = Order()
        order.orderType = "MKT"
        order.action = side.value
        order.totalQuantity = Decimal(qty)
        order.tif = tif
        order.conditionsIgnoreRth = True
        order.outsideRth = True
        
        order.algoStrategy = "Adaptive"
        order.algoParams = [TagValue("adaptivePriority", "Normal")] # type: ignore
        
        if orderRef is not None:
            order.orderRef = orderRef
        
        orderId = self._placeOrder(contract, order)
        return orderId
    
    
    def placeStopAdjTrailOrder(self, conId: int, side: Side, qty: float, stop_price: float, trigger_price: float, trail_amount: float, orderRef: Optional[str] = None, tif: str="GTC") -> Tuple[int, str]:
        
        contract = self.contracts[conId].contract
        
        order = Order()
        order.orderType = "STP"
        order.action = side.value
        order.auxPrice = stop_price
        order.totalQuantity = Decimal(qty)
        order.tif = tif
        order.conditionsIgnoreRth = True
        order.outsideRth = True
        
        order.adjustableTrailingUnit = 0
        order.triggerPrice = trigger_price
        order.adjustedOrderType = "TRAIL"
        order.adjustedTrailingAmount = trail_amount
        order.adjustedStopPrice = stop_price
        
        if orderRef is not None:
            order.orderRef = orderRef
        
        log.debug(f"orderRef: {order.orderRef}, action: {order.action}, auxPrice: {order.auxPrice}, triggerPrice: {order.triggerPrice}, adjustedTrailingAmount: {order.adjustedTrailingAmount}, adjustedStopPrice: {order.adjustedStopPrice}")
        
        orderId = self._placeOrder(contract, order)
        return orderId
    
    
    def _placeOrder(self, contract: Contract, order: Order) -> Tuple[int, str]:
        if not ALLOW_TRADING:
            log.warning("Trading is not allowed")
            return None, None
        
        if not self.isConnected():
            return None, None
        
        orderId = self.nextOrderId()
        log.debug(f"ORDER PLACING: {orderId} - {contract} - {order} - {order.orderRef}")
        super().placeOrder(orderId, contract, order)
        return orderId, order.orderRef
        
        
    def cancelOrder(self, orderId: int): # type: ignore
        log.debug(f"ORDER CANCELLING: {orderId}")
        super().cancelOrder(orderId, OrderCancel())
        
    
    ##### Contracts #####
    def registerContract(self, name2: str, conId: int, exchange: str, timeout=10) -> ContractDetails:
        if conId in self.contracts:
            return self.contracts[conId]
        
        self._contract_details_complete.clear()  # Reset the event
        self._contract_details_requests[conId] = name2
        
        self.reqContractDetails(name2, conId, exchange, timeout)
        
        if self._contract_details_complete.wait(timeout):
            return self.contracts[conId]
        else:
            raise TimeoutError(f"registerContract for {name2} timed out after 30 seconds")
        
    
    def reqContractDetails(self, name2: str, conId: int, exchange: str, timeout=10): # type: ignore
        contract = Contract()
        contract.conId = conId
        contract.exchange = exchange
        log.debug(f"Requesting contract details for {name2} - {contract}")
        super().reqContractDetails(-1, contract)
        
        
    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        name2 = self._contract_details_requests.pop(contractDetails.contract.conId)
        log.debug(f"Contract details Received - {name2} - {contractDetails}")
        
        self.contracts[contractDetails.contract.conId] = contractDetails
    
    def contractDetailsEnd(self, reqId: int):
        # super().contractDetailsEnd(reqId)
        self._contract_details_complete.set()  # Signal completion
    
    
     
    ##### System Events #####
    def nextValidId(self, orderId: int):
        log.debug(f"NextValidId: {orderId}")
        self.nextValidOrderId = orderId
        if not self.initialized:
            log.info("not initialized, requesting current time")
            self.reqCurrentTime()
            

            
    def nextOrderId(self) -> int:
        """Get the next valid order ID and increment the counter.
        Call this for the reqId when sending an order

        Returns:
            int: Next valid order ID
        """
        self.nextValidOrderId += 1
        return self.nextValidOrderId
    
    
    
    def currentTime(self, t: int):
        self.initialized = True
        # log.debug(f"Current time from server: {datetime.fromtimestamp(t)}")
        

        
    def error(self, reqId: int, errorTime: int, errorCode: int, errorString: str, advancedOrderRejectJson=""):
        
        if errorCode in [326]: # cannot connect
            log.error(f"Cannot connect to IB: {errorString}. EXITING")
            sys.exit(1)
        
        if errorCode in [2103, 2104, 2105, 2106, 2107, 2157, 2158]: # sec-def farm wierdness
            return
        
        elif errorCode == 202:
            return
        
        elif errorCode in [504]: # tws-client connectivity
            log.warning(f"Reconnecting to TWS/Gateway - {errorString}")
            self.connect(self._host, self._port, self._clientId)
            
        elif errorCode in [1100, 1102]: # ib-tws connectivity
            log.warning(errorString)
            return
        
        elif errorCode in [2109]: # Order event warning 
            log.warning(errorString)
            return
        
        else:
            log.error(f"Error - reqId: {reqId}, errorTime: {errorTime}, errorCode: {errorCode}, errorString: {errorString}, advancedOrderRejectJson: {advancedOrderRejectJson}")
    
    
    ##### Account Summary #####
    
    def accountSummary(self, reqId: int, account: str, tag: str, value: str, currency: str):
        # super().accountSummary(reqId, account, tag, value, currency)
        log.debug(f"Account summary         - {vars()}")

    
    def accountSummaryEnd(self, reqId: int):
        # super().accountSummaryEnd(reqId)
        log.debug(f"Account summary end     - {vars()}")


    ##### Open Orders #####
    
    def reqOpenOrders(self):
        """Request open orders - standard IBAPI method"""
        self.openOrders = {}
        self._open_orders_complete.clear()  # Reset the event
        super().reqOpenOrders()
    
    
    def openOrder(self, orderId: int, contract: Contract, order: Order, orderState: OrderState):
        # super().openOrder(orderId, contract, order, orderState)
        log.info(f"order update - status: {orderState.status}, orderId: {orderId}, permId: {order.permId}")
        log.debug(f"order update - {order}")
        log.debug(f"orderState: {orderState}")
        
        if orderState.status in ['PreSubmitted', 'Submitted']:
            self.addOpenOrder(order, orderState, contract)
            # self.permId2conId[order.permId] = contract.conId
            # self.openOrders[order.permId] = order
            
        
        
        
    def orderStatus(self, orderId: int, status: str, filled: Decimal, remaining: Decimal, avgFillPrice: float, permId: int, parentId: int, lastFillPrice: float, clientId: int, whyHeld: str, mktCapPrice: float):
        # super().orderStatus(orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
        if permId in self.openOrders and status in ['Cancelled', 'Inactive', 'ApiCancelled']:
            log.debug(f"removing open order: {permId}")
            self.removeOpenOrder(permId)
    
    
    def openOrderEnd(self):
        # super().openOrderEnd()
        log.debug("openOrderEnd")
        self._open_orders_complete.set()  # Signal completion

    
    def getOpenOrders(self, timeout=30) -> Dict[int, Order]:
        """Request open orders with blocking behavior"""
        self.reqOpenOrders()
        
        if self._open_orders_complete.wait(timeout):
            return self.openOrders
        else:
            raise TimeoutError(f"reqOpenOrders timed out after {timeout} seconds")


    def addOpenOrder(self, order: Order, orderState: OrderState, contract: Contract):
        if order.permId in self.openOrders:
            event_type = "updated"
            # log.debug(f"Updating open order: {order.permId} - {order}")
        else:
            event_type = "added"
            log.info(f"[ORDER]status: {orderState.status} - {self.order2String(order)}")    

        self.openOrders[order.permId] = order
        self.permId2conId[order.permId] = contract.conId
        self.onOrder(order, contract, event_type)


    def removeOpenOrder(self, permId: int):
        if permId in self.openOrders:
            log.info(f"[ORDER] Removed open order: {permId}")
            self.onOrder(self.openOrders[permId], self.contracts[self.permId2conId[permId]].contract, "removed")
            del self.permId2conId[permId]
            del self.openOrders[permId]


    def cancelAllOpenOrders(self):
        for permId, order in self.openOrders.items():
            # f"Cancelling open order: {permId}"
            self.cancelOrder(order.orderId)


    def onOrder(self, order: Order, contract: Contract, event_type: str):
        for callback in self._onOrder_callbacks:
            callback(order=order, contract=contract, event_type=event_type)
            
    def register_onOrder(self, callback: Callable):
        """
        Register a callback for onOrder events.
        
        Callback signature: callback(order: Order, contract: Contract)
        
        Args:
            callback: Function to call when an order is received. Should accept an Order object as an argument.
            
        """
        if callback in self._onOrder_callbacks:
            return
        
        # check function signature number and types
        sig = inspect.signature(callback)
        params = list(sig.parameters.values())
        positional = [p for p in params if p.kind == inspect.Parameter.POSITIONAL_ONLY]
        if len(positional) > 0:
            raise ValueError("Callback must not accept any positional arguments")
        
        self._onOrder_callbacks.append(callback)
        log.debug(f"Registered on_order callback: {callback.__name__}")
        
    def unregister_onOrder(self, callback: Callable):
        if callback not in self._onOrder_callbacks:
            return
        self._onOrder_callbacks.remove(callback)
        

    ##### Executions #####
    
    def reqExecutions(self, reqId: int, execFilter: ExecutionFilter):
        """Request executions - standard IBAPI method"""
        self.executions = {}
        self.commission_reports = {}
        self._executions_complete.clear()  # Reset the event
        super().reqExecutions(reqId, execFilter)
    
    
    def execDetails(self, reqId: int, contract: Contract, execution: Execution):
        # super().execDetails(reqId, contract, execution)
        log.debug(f"execDetails             - {execution}")
        self.executions[execution.execId] = execution
        
        
    def commissionAndFeesReport(self, commissionAndFeesReport: CommissionAndFeesReport):
        # super().commissionAndFeesReport(commissionAndFeesReport)
        log.debug(f"commissionAndFeesReport - {commissionAndFeesReport}")
        self.commission_reports[commissionAndFeesReport.execId] = commissionAndFeesReport
        
        execution = self.executions[commissionAndFeesReport.execId]
        order = self.openOrders[execution.permId]
        contract = self.contracts[self.permId2conId[order.permId]].contract
        
        fill = Fill.from_ib_objects(
            contract=contract,
            order=order,
            execution=execution,
            commission_report=commissionAndFeesReport,
            trade_date = datetime.now().astimezone(TZ).date()
        )
        self._onFill(fill)
    
    
    def execDetailsEnd(self, reqId: int):
        # super().execDetailsEnd(reqId)
        log.debug(f"execDetailsEnd          - {vars()}")
        self._executions_complete.set()  # Signal completion
    
    
    def getExecutions(self, reqId: int, execFilter: ExecutionFilter, timeout=30) -> Dict[str, Execution]:
        """Request executions with blocking behavior"""
        self.reqExecutions(reqId, execFilter)
        
        if self._executions_complete.wait(timeout):
            return self.executions
        else:
            raise TimeoutError(f"reqExecutions timed out after {timeout} seconds")
        


    ##### Fills #####

    def _onFill(self, fill: Fill):
        log.info(f"[FILL] - {fill}")
        for callback in self._onFill_callbacks:
            callback(fill)
            
        order = self.openOrders[fill.execution.permId]
        if fill.execution.cumQty == order.totalQuantity:
            log.debug(f"order fully filled: {fill.execution.permId}")
            self.removeOpenOrder(fill.execution.permId)
            

    
    def register_onFill(self, callback: Callable):
        """
        Register a callback for onFill events.
        
        Callback signature: callback(fill: Fill, orderId: int)
        
        Args:
            callback: Function to call when a fill is received. Should accept a Fill object as an argument.
            
        """
        if callback in self._onFill_callbacks:
            return
        
        # check function signature number and types
        sig = inspect.signature(callback)
        params = list(sig.parameters.values())
        positional = [p for p in params if p.kind == inspect.Parameter.POSITIONAL_OR_KEYWORD]
        if len(positional) < 1:
            raise ValueError("Callback must accept at least 1 positional argument: fill: Fill")
        if not list(sig.parameters.values())[0].annotation == Fill:
            raise ValueError("Callback first argument must be Fill")
        
        self._onFill_callbacks.append(callback)
        log.debug(f"Registered on_fill callback: {callback.__name__}")
        
    def unregister_onFill(self, callback: Callable):
        if callback not in self._onFill_callbacks:
            return
        self._onFill_callbacks.remove(callback)
        log.debug(f"Unregistered on_fill callback: {callback.__name__}")


    ##### Properties #####

    @property
    def initialized(self):
        return self._initialized
    
    @initialized.setter
    def initialized(self, value):
        if value != self._initialized:
            log.debug(f"IBClient initialized: {value}")
        self._initialized = value



    @classmethod
    def order2String(cls, order: Order) -> str:
        
        price_string = ""
        if order.orderType == "LMT":
            price_string = f", lmtPrice: {order.lmtPrice}"
        elif order.orderType == "STP":
            price_string = f", auxPrice: {order.auxPrice}"
        elif order.orderType == "MKT":
            price_string = "MKT"
        else:
            price_string = f", unknown price: {order.lmtPrice}"
        
        ret = (
            f"orderId: {order.orderId}, permId: {order.permId}, orderType: {order.orderType}, side: {order.action}, totalQuantity: {order.totalQuantity}"
            f", {price_string}"
            f", tif: {order.tif}, orderRef: {order.orderRef}"
        )
        
        return ret
    
     
    @classmethod
    def order2dict(cls, order: Order) -> Dict[str, Any]:
        ret = {
            'orderId': order.orderId,
            'orderType': order.orderType,
            'action': order.action,
            'totalQuantity': order.totalQuantity,
            'lmtPrice': order.lmtPrice,
            'auxPrice': order.auxPrice,
            'tif': order.tif,
            'orderRef': order.orderRef,
            'triggerPrice': order.triggerPrice,
            'adjustedOrderType': order.adjustedOrderType,
            'adjustedTrailingAmount': order.adjustedTrailingAmount,
            'adjustedStopPrice': order.adjustedStopPrice,
            'adjustableTrailingUnit': order.adjustableTrailingUnit,
            'algoStrategy': order.algoStrategy,
            # 'algoParams': order.algoParams,
        }
        for k in ret:
            if isinstance(ret[k], Decimal):
                ret[k] = float(ret[k])
        ret = cls.clean_ib_response(ret)
        return ret
    
    @classmethod
    def execution2dict(cls, execution: Execution) -> Dict[str, Any]:
        ret = {
            'time': execution.time,
            'side': execution.side,
            'shares': execution.shares,
            'price': execution.price,
            'orderRef': execution.orderRef,
            'execId': execution.execId,
            'orderId': execution.orderId,
            'exchange': execution.exchange,
        }
        for k in ret:
            if isinstance(ret[k], Decimal):
                ret[k] = float(ret[k])
        ret = cls.clean_ib_response(ret)
        return ret


    @classmethod
    def clean_ib_value(cls, value):
        """Convert IB's max float values to None"""
        if isinstance(value, float) and value >= sys.float_info.max:
            return None
        return value

    @classmethod
    def clean_ib_response(cls, data):
        """Recursively clean IB response data"""
        if isinstance(data, dict):
            return {k: cls.clean_ib_response(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [cls.clean_ib_response(item) for item in data]
        else:
            return cls.clean_ib_value(data)