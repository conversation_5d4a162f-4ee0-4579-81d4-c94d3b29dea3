__all__ = ["Portfolio"]

import os
import time
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

import orjson

from tradertools.clients import get_pg_client
from tradertools.clients import RedisClient

from hmmer._parameters import Parameter
from ._productgroup import ProductGroup
from ._fill import Fill
from ._natsinterface import NatsInterface
from .ibclient import IBClient
from ._teams import post_to_teams


log = logging.getLogger(__name__)

REQUEST_CHANNEL = "strategies.hmmer.requests"

class Portfolio:
    def __init__(self, ni: Optional[NatsInterface] = None, r: Optional[RedisClient] = None, ib: Optional[IBClient] = None, can_write_db: bool = False):
        
        self.ni = ni
        self.r = r
        self.ib = ib
        self.can_write_db = can_write_db
        
        self.products: Dict[str, ProductGroup] = {}
        
        
        self._initialized = False
        log.info("Created Portfolio")
        
        
    
    def initialize(self):
        
        if self.ni is not None:
            log.debug("Subscribing to nats")
            self.ni.nats.subscribe(REQUEST_CHANNEL, self.nats_request_handler)
        
        if self.ib is not None:
            self.ib.register_onFill(self.process_fill)
            self.ib.register_onOrder(self.onOrder)
            
        
        params = self.query_parameters()
        for param in params:
            # if param.product != 'ZC': continue
            if param.product not in self.products:
                # if param.product != 'GC': continue
                self.products[param.product] = ProductGroup(param, self.ib, self.ni, self.can_write_db)
        
        fills = self.query_fills()
        for fill in fills:
            self.process_fill(fill, from_db=True)
                
        for product in self.products.values():
            product.initialize()
            
        self.initialized = True
        
        
    def onOrder(self, **kwargs):
        if not self.initialized:
            return
        self.update_nats_orders(**kwargs)
        self.update_redis_orders(**kwargs)
        self.update_redis_products(**kwargs)
            
            
    def cleanup(self):
        for product in self.products.values():
            product.cleanup()
        log.info("Cleaned up Portfolio")
        
            
    def update(self, update_bars: bool = False):
        for product in self.products.values():
            product.update(update_bars=update_bars)
        self.update_redis_summary()
        self.update_redis_orders()
        self.update_redis_products()
        self.update_redis_contracts()
        
            
            
    def process_fill(self, fill: Fill, from_db: bool = False):
        if fill.prd in self.products:
            self.products[fill.prd].process_fill(fill, from_db)
            
        if not from_db:
            self.update_redis_summary()
            post_to_teams(f"New Fill: {fill}")
            
            
            
    def update_nats_orders(self, order = None, contract = None, event_type: str = None):
        if self.ni is None:
            return
        self.ni.put('orders', self.openOrders())
        
        
    def update_redis_orders(self, order = None, contract = None, event_type: str = None):
        if self.r is None:
            return
        self.r.json_set('hmmer.orders', '$', self.openOrders())
        
    def update_redis_summary(self):
        if self.r is None:
            return
        self.r.json_set('hmmer.summary', '$', self.summary())
        
    def update_redis_products(self, **kwargs):
        if self.r is None:
            return
        self.r.json_set('hmmer.products', '$', self.summary_products())
        
    def update_redis_contracts(self, **kwargs):
        if self.r is None:
            return
        self.r.json_set('hmmer.contracts', '$', self.summary_contracts())
            
            
    def query_parameters(self) -> List[Parameter]:
        start = time.perf_counter()
        pg = get_pg_client(os.getenv("POSTGRES_CLIENT")) # type: ignore
        
        query = """
        SELECT * 
            FROM hmmer.parameters
            WHERE ignore = false
        """
        params = pg.query(query)
        params = [Parameter.from_db_row(p) for p in params]
        
        log.info(f"Downloaded parameters in {time.perf_counter() - start:.2f}s")
        return params
    
    
        
    def query_fills(self) -> List[Fill]:
        start = time.perf_counter()
        pg = get_pg_client(os.getenv("POSTGRES_CLIENT")) # type: ignore
        
        query = """
        SELECT * 
            FROM hmmer.fills
            ORDER BY dt
        """
        fills = pg.query(query)
        fills = [Fill.from_db_dict(f) for f in fills]
        
        log.info(f"Downloaded {len(fills)} fills in {time.perf_counter() - start:.2f}s")
        return fills


    ##### Reporting #####
    
    @property
    def totalPL(self) -> float:
        return round(sum([product.totalPL for product in self.products.values()]), 8)
    
    @property
    def dailyPL(self) -> float:
        return round(sum([product.performance.dailyPL for product in self.products.values()]), 8)
    
    @property
    def fees(self) -> float:
        return round(sum([product.fees for product in self.products.values()]), 8)
    
    @property
    def volume(self) -> float:
        return sum([product.volume for product in self.products.values()])
    
    def openOrders(self, msg: str=None) -> List[Dict[str, Any]]:
        if self.ib is not None:
            return [self.ib.order2dict(order) for order in self.ib.openOrders.values()]
        return []
    
    def openOrdersDict(self, msg: str=None) -> Dict[int, Dict[str, Any]]:
        if self.ib is not None:
            return {permId: self.ib.order2dict(order) for permId, order in self.ib.openOrders.items()}
        return {}
    
    def fills(self, msg: str=None) -> List[Dict[str, Any]]:
        return sorted([fill.summary() for product in self.products.values() for fill in product.fills], key=lambda x: x['datetime'], reverse=True)
    
    def current_intervals(self, msg: str=None) -> Dict[str, datetime]:
        return {product.name: product.current_interval for product in self.products.values()}
    
    def current_bars(self, msg: str=None) -> Dict[str, datetime]:
        return {product.name: product.current_bar for product in self.products.values()}
    
    
    def summary(self, msg: str=None) -> Dict[str, Any]:
        return {
            'totalPL': self.totalPL,
            'dailyPL': self.dailyPL,
            'fees': self.fees,
            'volume': self.volume,
            'openOrders': len(self.openOrders()),
        }
    
    def summary_products(self, msg: str=None) -> List[Dict[str, Any]]:
        return [product.summary() for product in self.products.values()]
    
    def summary_product_performance(self, msg: str=None) -> List[Dict[str, Any]]:
        return [product.performance.summary() for product in self.products.values()]
    
    def summary_contracts(self, msg: str=None) -> List[Dict[str, Any]]:
        return [contract.summary() for product in self.products.values() for contract in product.contracts.values()]
    
    def properties_products(self, msg: str=None) -> List[Dict[str, Any]]:
        return [product.properties() for product in self.products.values()]


    def ib_connected(self, msg: str=None) -> bool:
        if self.ib is not None:
            return self.ib.isConnected()
        return False
    
    
    ##### Control Functions #####
    
    def contract_reset_attempts(self, msg: str=None) -> {dict[str, Any]}:
        try:
            for product in self.products.values():
                for contract in product.contracts.values():
                    contract.reset_order_attempts()
        except Exception as e:
            log.error(f"Failed to reset contract attempts: {e}")
            return {'success': False, 'error': str(e)}
        return {'success': True}

    ##### Nats Listening #####
    
    def nats_request_handler(self, msg: Any) -> Any:
        # log.debug(f"Received nats request: {msg}")
        
        if not isinstance(msg, str):
            payload = "must give function name as string"
        if msg in self.nats_functions:
            payload = self.nats_functions[msg](msg)
        else:
            payload = f"Invalid function name: {msg}, Available functions: {list(self.nats_functions.keys())}"
        # log.debug(f"Sending nats response: {payload}")
        return orjson.dumps(payload)

    @property
    def nats_functions(self) -> Dict[str, Callable]:
        return {
            'openOrders': self.openOrders,
            'fills': self.fills,
            'current_intervals': self.current_intervals,
            'current_bars': self.current_bars,
            'summary': self.summary,
            'summary_products': self.summary_products,
            'summary_product_performance': self.summary_product_performance,
            'summary_contracts': self.summary_contracts,
            'properties_products': self.properties_products,
            'ib_connected': self.ib_connected,
            'contract_reset_attempts': self.contract_reset_attempts
        }
        
        
    @property
    def initialized(self) -> bool:
        return self._initialized
    @initialized.setter
    def initialized(self, value: bool):
        if value != self._initialized:
            log.info(f"Portfolio initialized: {value}")
        self._initialized = value

    def print(self):
        for product in self.products.values():
            print(product)
