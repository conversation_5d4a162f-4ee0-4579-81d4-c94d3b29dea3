__all__ = ["Performance"]

import os
import logging
from typing import Dict, Any
from datetime import date

from tradertools.clients import get_pg_client
from tradertools.instruments import get_definition

from ._bar import Bar

log = logging.getLogger(__name__)



class Performance:
    
    def __init__(self, product: str, can_write_db: bool = False):
        
        self.product = product
        self.can_write_db = can_write_db
        
        self.inst = get_definition(product=product)
        self.pointValue = float(self.inst.pointValue)
        
        # self.data: list[PerfRow] = []
        self.buyNotional: float = None
        self.sellNotional: float = None
        
        self.tradeDate: date = None
        self.name2: str = None
        self.open: float = None
        self.close: float = None
        self.position: float = 0.0
        self.dailyPL: float = 0.0
        self.dailyFees: float = 0.0
        
        
        
    def initialize(self, tradeDate: date, name2: str, incoming_position: float, previous_close: float):
        
        self.incoming_position = incoming_position
        
        self.buyNotional = 0.0
        self.sellNotional = 0.0
        
        self.dailyPL = 0.0
        self.dailyFees = 0.0
        
        self.tradeDate = tradeDate
        self.name2 = name2
        self.position = 0
        self.open = previous_close
        self.close = previous_close
        
        log.debug(f"Initialized performance: {tradeDate} - {name2} - {incoming_position} - {previous_close}")
        
        # notional = abs(incoming_position) * previous_close
        if incoming_position > 0:
            self.update_position(incoming_position, "BUY", previous_close, 0.0)
        else:
            self.update_position(-incoming_position, "SELL", previous_close, 0.0)
        
            
            
    def roll(self, old_bar: Bar, new_bar: Bar):
        log.debug(f"Rolling performance: {old_bar.tradeDate} - {new_bar.tradeDate}")
        if self.position > 0:
            self.sellNotional += self.position * old_bar.close
        elif self.position < 0:
            self.buyNotional += -self.position * old_bar.close
            
        self.close = old_bar.close
        self.calculate_daily_pl()
        self.write_row()
        
        self.initialize(new_bar.tradeDate, new_bar.name2, self.position, new_bar.open)
            
        

    def update_position(self, qty: float, side: str, price: float, fees: float=0.0):
        if side == "BUY":
            self.position += qty
            self.buyNotional += qty * price
        elif side == "SELL":
            self.position -= qty
            self.sellNotional += qty * price
        else:
            raise ValueError(f"Unknown side: {side}")
            
        log.debug(f"{self.name2} - Updated position to: {self.position} after {qty} {side} at {price}")
            
        
        self.close = price
        self.dailyFees += fees
        self.calculate_daily_pl()
        self.write_row()
        
        
    def update_price(self, price: float):
        self.close = price
        self.calculate_daily_pl()
        self.write_row()
        
    def calculate_daily_pl(self):
        pl = (self.sellNotional - self.buyNotional) + self.position * self.close
        pl = round(pl * self.pointValue, 2)
        if pl != pl:
            pl = 0.0
        if pl == -0.0:
            pl = 0.0
        self.dailyPL = pl



    def write_row(self):
        if not self.can_write_db:
            return
        pg = get_pg_client(os.getenv("POSTGRES_CLIENT"))
        
        row = self.db_row()
        try:
            pg.upsert("hmmer.performance", row, conflict_target=['tradeDate', 'product'])
            # log.debug(f"Recorded performance: {row}")
        except Exception as e:
            log.error(f"Error writing performance row: {e}")
        
        
        
        
    def db_row(self) -> Dict[str, Any]:
        return {
            'tradeDate': self.tradeDate,
            'product': self.product,
            'name2': self.name2,
            'open': self.open,
            'close': self.close,
            'position': self.position,
            'dailyPL': self.dailyPL,
            'dailyFees': self.dailyFees,
        }

    def summary(self) -> Dict[str, Any]:
        return {
            **self.db_row(),
            'pointValue': self.pointValue,
            'buyNotional': self.buyNotional,
            'sellNotional': self.sellNotional,
            'incoming_position': self.incoming_position,
            'can_write_db': self.can_write_db,
        }
