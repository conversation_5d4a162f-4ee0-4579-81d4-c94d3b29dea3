__all__ = ['Bars']

import os
import time  
import inspect
import logging
from typing import Optional
from datetime import datetime 
from dateutil.relativedelta import relativedelta
from zoneinfo import ZoneInfo
from typing import List, Callable

import pandas as pd

from tradertools.clients import get_pg_client

from ._bar import Bar

log = logging.getLogger(__name__)

YEARS_OF_BARS = 3
TZ = ZoneInfo("America/New_York")

class Bars:
    def __init__(self, product: str):
        self.product = product
        
        self.bars: List[Bar] = []
        self.df: pd.DataFrame = pd.DataFrame()
        
        self._onBar_callbacks: List[Callable] = []
        self._onNewTradeDate_callbacks: List[Callable] = []
        
    
    
    def initialize(self):
        self.bars = self.query_bars()
        self._calc_df() 
    
    

    
    def query_bars(self, from_et: Optional[datetime] = None, to_et: Optional[datetime] = None) -> List[Bar]:
        start = time.perf_counter()
        
        # product
        where_clause = f"WHERE prd = '{self.product}'"
        
        # from time
        if from_et is None and len(self.bars) == 0:
            from_et = datetime.now(TZ) - relativedelta(years=YEARS_OF_BARS)
            where_clause += f" AND et > '{from_et}'"
        elif from_et is not None:
            where_clause += f" AND et > '{from_et}'"
        
        # to time
        if to_et is None:
            to_et = datetime.now(TZ)
        where_clause += f" AND et <= '{to_et}'"
            
        query = f"""
        SELECT * 
            FROM hmmer.allbars
        {where_clause}
        ORDER BY et
        """
        client_name = os.getenv("POSTGRES_CLIENT") 
        bars = get_pg_client(client_name).query(query) # type: ignore
        bars = [Bar.from_db_dict(b) for b in bars]
        
        if len(bars) > 0:
            log.info(f"Downloaded {self.product} {len(bars)} bars in {time.perf_counter() - start:.2f}s")
        return bars
    
    
    def update(self):
        last_et = self.bars[-1].et
        new_bars = self.query_bars(from_et=last_et)
        if len(new_bars) == 0:
            return
        self.bars.extend(new_bars)
        self._calc_df()
        self.onBar()
        
        if len(new_bars) == 1:
            # if new_bars[0].tradeDate != new_bars[-1].tradeDate:
            if self.bars[-1].tradeDate != self.bars[-2].tradeDate:
                log.info(f"New tradeDate for {self.product}: {new_bars[-1].tradeDate}")
                self.onNewTradeDate(new_bars[0], new_bars[-1])


    def _calc_df(self):
        self.df = pd.DataFrame(self.bars)
        self.df.set_index('dt', inplace=True)
        self.df.sort_index(inplace=True)
        
        
    def onBar(self):
        for callback in self._onBar_callbacks:
            callback()
        
    def register_onBar(self, callback: Callable):
        
        if callback in self._onBar_callbacks:
            return
        
        # check function signature number and types
        sig = inspect.signature(callback)
        params = list(sig.parameters.values())
        positional = [p for p in params if p.kind == inspect.Parameter.POSITIONAL_OR_KEYWORD]
        if len(positional) > 0:
            raise ValueError("Callback must not accept any positional arguments")
        
        if callback in self._onBar_callbacks:
            return
        self._onBar_callbacks.append(callback)
        
        log.info(f"Registered on_bar callback: {callback.__name__}")
        
    def unregister_onBar(self, callback: Callable):
        if callback not in self._onBar_callbacks:
            return
        self._onBar_callbacks.remove(callback)
        
        log.info(f"Unregistered on_bar callback: {callback}")


    def onNewTradeDate(self, old_bar: Bar, new_bar: Bar):
        for callback in self._onNewTradeDate_callbacks:
            callback(old_bar, new_bar)

    def register_onNewTradeDate(self, callback: Callable):
        """
        Callback signature: callback(old_bar: Bar, new_bar: Bar)
        """
        if callback in self._onNewTradeDate_callbacks:
            return
        self._onNewTradeDate_callbacks.append(callback)
        
        log.info(f"Registered on_new_trade_date callback: {callback.__name__}")
        
    def unregister_onNewTradeDate(self, callback: Callable):
        if callback not in self._onNewTradeDate_callbacks:
            return
        self._onNewTradeDate_callbacks.remove(callback)
        
        log.info(f"Unregistered on_new_trade_date callback: {callback.__name__}")
    
    
    def previous_closing_bar(self) -> Bar | None:
        
        if len(self.bars) <= 2:
            return None
        
        for bar in reversed(self.bars):
            if bar.tradeDate != self.bars[-1].tradeDate:
                return bar
        return None
    
    @property
    def previous_close(self) -> float | None:
        previous_bar = self.previous_closing_bar()
        if previous_bar is None:
            return None
        return self.previous_closing_bar().close
        
    
    
    def __len__(self) -> int:
        return len(self.bars)
    
    def __getitem__(self, index: int) -> Bar:
        return self.bars[index]
    
    def __iter__(self):
        return iter(self.bars)
    
    def __reversed__(self):
        return reversed(self.bars)