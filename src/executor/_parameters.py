# __all__ = ["Parameters"]


# from dataclasses import dataclass
# from typing import Dict, Any
# from datetime import datetime, date, time, timedelta


# @dataclass
# class Parameters:
#     product: str
#     multiplier: float
#     tas_est: time
#     sunday_open: time
#     shiftdays: int
#     size: float
#     fd1_window: int
#     fd1_d: float
#     fd2_window: int
#     fd2_d: float
#     norm_position_window: int
#     smoothing_window: int
#     smoothing_alpha: float
#     covariance_type: str
#     mthsback: int
#     sticky_factor: float
#     window_size: int
#     forward_bars: int
#     stop: float
#     stopadj: float
#     trail: float


#     @classmethod
#     def from_db_row(cls, db_dict: Dict[str, Any]) -> 'Parameters':
#         return cls(
#             product=db_dict['product'],
#             multiplier=db_dict['multiplier'],
#             tas_est=db_dict['tas_est'],
#             sunday_open=db_dict['sunday_open'],
#             shiftdays=db_dict['shiftdays'],
#             size=db_dict['size'],
#             fd1_window=db_dict['fd1_window'],
#             fd1_d=db_dict['fd1_d'],
#             fd2_window=db_dict['fd2_window'],
#             fd2_d=db_dict['fd2_d'],
#             norm_position_window=db_dict['norm_position_window'],
#             smoothing_window=db_dict['smoothing_window'],
#             smoothing_alpha=db_dict['smoothing_alpha'],
#             covariance_type=db_dict['covariance_type'],
#             mthsback=db_dict['mthsback'],
#             sticky_factor=db_dict['sticky_factor'],
#             window_size=db_dict['window_size'],
#             forward_bars=db_dict['forward_bars'],
#             stop=db_dict['stop'],
#             stopadj=db_dict['stopadj'],
#             trail=db_dict['trail']
#         )
        
        
        
        
#     def get_tradeDate(self, dt: datetime) -> date:

#         if dt.time() > self.tas_est:
#             dt = (dt + timedelta(days=1))
        
#         while dt.weekday() >= 5:
#             dt = (dt + timedelta(days=1))
        
#         return dt.date()
        
