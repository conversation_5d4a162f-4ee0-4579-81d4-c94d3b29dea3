__all__ = ["ProductGroup"]

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from ibapi.order import Order

from hmmer._parameters import Parameter
from ._contract import Contract
from ._bars import Bars, Bar
from ._fill import Fill
from .ibclient import IBClient
from ._natsinterface import NatsInterface

from .types.enums import Side, OrderReason, TradingState
from .types.constants import OPEN_TIME, BAR_HOURS, TZ 

from ._performance import Performance

log = logging.getLogger(__name__)

BUCKET = "hmmer"

class ProductGroup:
    def __init__(self, parameter: Parameter, ib: Optional[IBClient] = None, nats: Optional[NatsInterface] = None, can_write_db: bool = False):
        
        self.parameter = parameter
        self.ib = ib
        self.nats = nats
        self.can_write_db = can_write_db
        
        self.contracts: Dict[str, Contract] = {}
        
        self.bars = Bars(self.name)
        
        self.totalPL = 0.0
        self.position = 0
        self.fees = 0.0
        self.buyFees = 0.0
        self.sellFees = 0.0
        self.buyNotional = 0.0
        self.sellNotional = 0.0
        self.volume = 0.0
        self.buyVolume = 0.0
        self.sellVolume = 0.0
        
        self.performance = Performance(self.name, can_write_db)
        
        self._regime_latch = False
        
        self._initialized = False
        
        log.info(f"Created ProductGroup: {self.name}")
        
        
        
    def initialize(self):
        
        self.load_state()
        
        self.bars.initialize()
        if self.current_contract.name not in self.contracts:
            self.contracts[self.current_bar.name2] = Contract(self.current_bar.name2, self.parameter, self.ib, self.nats)
        
        for contract in self.contracts.values():
            contract.initialize()
            contract.can_trade = self.parameter.can_trade
        
        # Initialize performance
        daily_position = 0
        daily_fills = []
        for f in self.current_contract.fills:
            if f.tradeDate == self.current_bar.tradeDate:
                if f.side == Side.BUY:
                    daily_position += f.qty
                else:
                    daily_position -= f.qty
                daily_fills.append(f)
        self.performance.initialize(
            self.current_bar.tradeDate, 
            self.current_bar.name2, 
            self.position-daily_position, # incoming position is position - daily position
            self.bars.previous_close
        )
        for f in daily_fills:
            self.performance.update_position(f.qty, f.side.value, f.price, f.fees) 
            
        self.bars.register_onNewTradeDate(self.rollTradeDate)
        
        self.calc_pl()
        self.initialized = True
        
        log.info(f"Initialized ProductGroup: {self.name}, can_trade: {self.parameter.can_trade}")
        
    
        
    ##### State Management #####
    @property
    def state_key(self) -> str:
        return f"state.products.{self.name}"
    
    def load_state(self):
        state = {}
        try:
            state = self.nats.nats.get_kv(BUCKET, self.state_key)
            log.info(f"Loading state for {self.name}: {state}")
        except Exception as e:
            log.warning(f"Unable to load state for {self.name}: {e}")
            
        # regime latch
        self.regime_latch = state.get('regime_latch', True)
        
        
        
    def save_state(self):
        # TODO - remove once dash doesn't use this
        if self.ib is None:
            return
        state = {
            'regime_latch': self.regime_latch
        }
        self.nats.nats.put_kv(BUCKET, self.state_key, state)
    
        
        
    def cleanup(self):
        log.info(f"Cleaning up ProductGroup: {self.name}")
        self.save_state()
        for contract in self.contracts.values():
            contract.cleanup()
        
        
        
    def rollTradeDate(self, old_bar: Bar, new_bar: Bar):
        self.save_state()
        self.rollPerformance(old_bar, new_bar)
        for contract in self.contracts.values():
            contract.rollTradeDate()
        
        
    
    def process_fill(self, fill: Fill, from_db: bool = False):
        if fill.name2 not in self.contracts:
            self.contracts[fill.name2] = Contract(fill.name2, self.parameter, self.ib, self.nats)
            
        if from_db:
            self.contracts[fill.name2].process_fill(fill)
            self.calc_pl()
            return
            
        fill.tradeDate = self.parameter.get_tradeDate(fill.dt)
        fill.write_to_db()
        
        self.performance.update_position(fill.qty, fill.side.value, fill.price, fill.fees)
            
        self.contracts[fill.name2].process_fill(fill)
        self.save_state()
        self.calc_pl()

    

    def calc_pl(self):
        totalPL = 0.0
        position = 0
        fees = 0.0
        buyFees = 0.0
        sellFees = 0.0
        volume = 0.0
        buyVolume = 0.0
        sellVolume = 0.0
        
        for contract in self.contracts.values():
            totalPL += contract.pl
            position += contract.position
            fees += contract.fees
            buyFees += contract.buyFees
            sellFees += contract.sellFees
            volume += contract.volume
            buyVolume += contract.buyVolume
            sellVolume += contract.sellVolume
        
        self.totalPL = round(totalPL, 8)
        self.position = position
        self.fees = round(fees, 8)
        self.buyFees = round(buyFees, 8)
        self.sellFees = round(sellFees, 8)
        self.volume = volume
        self.buyVolume = buyVolume
        self.sellVolume = sellVolume
        
    def rollPerformance(self, old_bar: Bar, new_bar: Bar):
            self.performance.roll(old_bar, new_bar)
            
    def cancelAllOpenOrders(self):
        for contract in self.contracts.values():
            contract.cancelAllOpenOrders()
    

            
    def update(self, update_bars: bool = False):
        if not self.initialized:
            return
        
        if update_bars:
            self.bars.update()
            self.save_state()   
            
        current_name2 = self.current_bar.name2
        
        # ROLL - Close postions from old contracts
        for contract in self.contracts.values():
            if update_bars:
                contract.update()
            # close positions from non current contracts
            if contract.name != current_name2:
                contract.close_position(OrderReason.ROLL)
                
        # check and release latch
        if self.regime_latch and self.current_regime == 0 and self.current_contract.position == 0:
            self.regime_latch = False
        
        
        self.performance.update_price(self.current_contract.markPrice)
        
        self.calc_pl()
        
        # initiate/adjust position
        position_differs = self.current_contract.position != self.desired_position
        no_working_entry = not self.current_contract.workingEntryExists
        no_pending_entry = self.current_contract.pendingPlacementOrderId is None
        is_trading = self.current_contract.tradingHours.is_trading
        can_trade = self.parameter.can_trade
        
        if not all([is_trading, can_trade]):
            return
        
        if (self.tradingState in [TradingState.READY, TradingState.ACTIVE] 
            and position_differs 
            and no_pending_entry
            and no_working_entry):
            
            position_delta = self.desired_position - self.current_contract.position
            size = abs(position_delta)
            side = Side.BUY if position_delta > 0 else Side.SELL
            
            log.info(f"ADJUSTING POSITION - {self.name} from {self.current_contract.position} to {self.desired_position}")
            self.current_contract.initiate_position(side, size)
            return
        
        # place stop
        entry_exists = self.current_contract.latest_entry is not None
        
        if (self.tradingState == TradingState.ACTIVE 
            and entry_exists 
            and not self.current_contract.workingStopExists
            and self.current_contract.position != 0):
            
            lep = self.current_contract.latest_entry.price # type: ignore
            
            qty = abs(self.current_contract.position)
            if self.current_contract.position > 0:
                side = Side.SELL
                stop_price = lep - self.parameter.stop 
                trigger_price = lep + self.parameter.stopadj
                
            else:
                side = Side.BUY
                stop_price = lep + self.parameter.stop
                trigger_price = lep - self.parameter.stopadj
            trail_amount = self.parameter.trail
            
            log.info(f"PLACING STOP - ct: {self.name}, orig_entry_price: {lep}, stop_price: {stop_price}, trigger_price: {trigger_price}, trail_amount: {trail_amount}") # type: ignore
            self.current_contract.placeStopAdjTrail(side, qty, stop_price, trigger_price, trail_amount)
            return
        
        # cancel stop
        # elif not entry_exists and self.current_contract.workingStopExists:
        elif (self.current_contract.position == 0 
            and self.current_contract.workingStopExists):   
            self.current_contract.cancelWorkingStop()
            return
            

        

    @property
    def name(self) -> str:
        return self.parameter.product 
    
        
    @property
    def current_interval(self) -> datetime:
        
        now = datetime.now(TZ)
    
        # Create the peg time for today
        peg_time = now.replace(hour=OPEN_TIME.hour, minute=OPEN_TIME.minute, second=0, microsecond=0)
        
        if now >= peg_time:
            # We're at or after peg time - iterate forward to find the right increment
            current_increment = peg_time
            while current_increment + timedelta(hours=BAR_HOURS) <= now:
                current_increment += timedelta(hours=BAR_HOURS)
            return current_increment
        else:
            # We're before peg time - iterate backward from yesterday's peg time
            current_increment = peg_time - timedelta(days=1)
            while current_increment + timedelta(hours=BAR_HOURS) <= now:
                current_increment += timedelta(hours=BAR_HOURS)
            return current_increment
        
    @property
    def current_bar(self) -> Bar:
        for bar in reversed(self.bars):
            if bar.et == self.current_interval:
                return bar
        else:
            return self.bars[-1]
        # raise ValueError(f"Current bar not found for interval: {self.current_interval} for {self.name}")
    
    @property
    def previous_bar(self) -> Bar:
        for bar in reversed(self.bars):
            if bar.et == self.current_interval - timedelta(hours=BAR_HOURS):
                return bar
        else:
            return self.bars[-2]
        
    
    @property
    def current_regime(self) -> int:
        return self.current_bar.regime
    
    @property
    def current_regime_start(self) -> datetime:
        for bar in reversed(self.bars):
            if bar.regime != self.current_regime:
                return bar.et
        return self.bars[0].et
    
    @property
    def tradingState(self) -> TradingState:
        latest_entry = self.current_contract.latest_entry
        latest_fill = self.current_contract.fills[-1] if self.current_contract.fills else None
        
        if self.regime_latch:
            return TradingState.WAITING
        
        if latest_entry is None:
            return TradingState.READY                    # Case 1
            
        if self.position != 0:
            return TradingState.ACTIVE                   # Case 4
            
        # Flat position with recent entry = stopped out
        if latest_fill is not None and 'stop' in latest_fill.orderRef:
            return TradingState.STOPPED_OUT              # Case 2
        
        return TradingState.READY
        # latest_entry = self.current_contract.latest_entry
        # latest_fill = self.current_contract.fills[-1] if self.current_contract.fills else None
        
        # has_traded = True if len(self.fills) > 0 else False
            
        # if self.position != 0:
        #     return TradingState.ACTIVE
            
        # # Flat position with recent entry = stopped out
        # if latest_fill is not None and 'stop' in latest_fill.orderRef:
        #     return TradingState.STOPPED_OUT              
        
        # if latest_entry is None:
        #     if not has_traded and self.position == 0 and self.current_regime != 0:
        #         return TradingState.STOPPED_OUT # here if no trades have occurred ever and we are in a regime
        
        # return TradingState.READY
        
    
    
    @property
    def desired_position(self) -> float:
        return self.current_regime * self.parameter.size
    
    
    @property
    def current_contract(self) -> Contract:
        if self.current_bar.name2 not in self.contracts:
            self.contracts[self.current_bar.name2] = Contract(self.current_bar.name2, self.parameter, self.ib, self.nats)
        return self.contracts[self.current_bar.name2]
    
    @property
    def previous_contract(self) -> Contract:
        return self.contracts[self.previous_bar.name2]
    
    @property
    def regime_latch(self) -> bool:
        return self._regime_latch
    @regime_latch.setter
    def regime_latch(self, value: bool):
        if value != self._regime_latch:
            log.info(f"Regime latch set to {value} for {self.name}")
        self._regime_latch = value

    @property
    def initialized(self) -> bool:
        return self._initialized
    @initialized.setter
    def initialized(self, value: bool):
        if value != self._initialized:
            log.info(f"ProductGroup {self.name} initialized: {value}")
        self._initialized = value
        
    
    
    @property
    def openOrders(self) -> List[Order]:
        if self.ib is not None:
            return [order for permId, order in self.ib.openOrders.items() if self.ib.permId2conId[permId] in [contract.instDef.conId for contract in self.contracts.values()]]
        return []
    
    
    @property
    def fills(self) -> List[Fill]:
        return sorted([fill for contract in self.contracts.values() for fill in contract.fills], key=lambda x: x.dt)
    
    
        
    def summary(self) -> Dict[str, Any]:
        return {
            'product': self.name,
            'contract': self.current_contract.name,
            'tradingState': self.tradingState.value,
            'totalPL': self.totalPL,
            'dailyPL': self.performance.dailyPL,
            'position': self.position,
            'daysToRoll': self.current_contract.daysToRoll,
            'openOrders': len(self.openOrders),
            **self.regime_stats(),
            'norm_position': round(self.current_bar.norm_position, 4),
            'can_trade': self.parameter.can_trade,
            'isTradingHrs': self.current_contract.tradingHours.is_trading,
            'rollDate': self.current_contract.rollDate.strftime("%m-%d"),
            'last_et': self.bars[-1].et.strftime("%m-%d %H:%M %Z"), 
        }
        
        
    def regime_stats(self) -> Dict[str, Any]:
        regime = self.current_regime
        regime_start_et = self.bars[-1].et
        regime_entry = self.bars[-1].close
        for bar in reversed(self.bars):
            if bar.regime != regime:
                break
            regime_start_et = bar.et
            regime_entry = bar.close
            
        duration_days = round((self.bars[-1].et - regime_start_et).total_seconds() / (24 * 3600), 1)
        regime_change = round(self.current_contract.markPrice - regime_entry, 8)
        return {
            'regime': regime,
            'rgm_time': f"{duration_days} days",
            'rgm_delta': regime_change,
            'rgm_start': regime_start_et.strftime("%m-%d %H:%M %Z"),
            'rgm_entry': regime_entry,
        }
        
        
    def properties(self) -> Dict[str, Any]:
        """
        Returns a dict of all properties of the product group.

        Returns:
            Dict[str, Any]: Dictionary of all properties.
        """
        props = {}
        for prop in dir(self):
            if not prop.startswith('_') and not callable(getattr(self, prop)):
                props[prop] = getattr(self, prop)
        return props
                
        
    

    def __str__(self) -> str:
        return f"ProductGroup: {self.name} - dailyPL: {self.performance.dailyPL}, totalPL: {self.totalPL}, position: {self.position}"

