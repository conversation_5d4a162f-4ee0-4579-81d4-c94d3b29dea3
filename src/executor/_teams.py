__all__ = ["post_to_teams"]

import os
import requests
import json
import logging

log = logging.getLogger(__name__)


FLOW_URL = os.getenv("FLOW_URL", None)
ENV = os.getenv("ENV", "development")


def post_to_teams(text: str, details: dict | None = None, url: str | None = FLOW_URL, timeout: int = 10) -> None:
    """Send a plain message (and optional dict of details) to the Teams Workflow."""
    if ENV == "development":
        return
    
    if not url: # or "logic.azure.com" not in FLOW_URL:
        log.warning("FLOW_URL is missing or invalid. Set FLOW_URL or edit the script.")
        return

    payload: dict = {"text": text}
    if details:
        payload["details"] = details

    # The flow’s trigger expects JSON. Post and raise if non-2xx.
    try:
        resp = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=timeout,
        )
        resp.raise_for_status()
    except requests.exceptions.RequestException as e:
        log.error(f"Failed to send Teams notification: {e}")
        return

    # Workflows often respond 202 (accepted). If you added a "Response" action,
    # you'll get 200 plus a JSON body you can print here.
    if resp.headers.get("Content-Type", "").startswith("application/json"):
        try:
            print(json.dumps(resp.json(), indent=2))
        except Exception:
            log.warning("Failed to parse JSON response: %s", resp.text)
            

