__all__ = ["Contract"]
import os
import sys
import logging
import math
from typing import Dict, List, Any, Optional
from datetime import datetime, date, timedelta

from ibapi.order import Order
from ibapi.contract import ContractDetails

from tradertools.instruments import Instrument, get_definition
from tradertools.marketdata.historical import get_bars_in_period

from .ibclient import IBClient
from ._natsinterface import NatsInterface
from ._fill import Fill
from ._tradinghours import TradingHours
from ._orderreflabel import OrderRefLabel
from .types.enums import Side, OrderReason, FillStatus
from hmmer._parameters import Parameter

log = logging.getLogger(__name__)

MAX_INITIATE_ATTEMPTS = int(os.getenv("MAX_INITIATE_ATTEMPTS", "4"))
MAX_STOPADJTRAIL_ATTEMPTS = int(os.getenv("MAX_STOPADJTRAIL_ATTEMPTS", "4"))
MAX_CLOSE_ATTEMPTS = int(os.getenv("MAX_CLOSE_ATTEMPTS", "4"))

BUCKET = "hmmer"


class Contract:
    def __init__(self, name: str, parameter: Parameter, ib: Optional[IBClient] = None, nats: Optional[NatsInterface] = None, strategy: str="hmmer"):
        
        self.name = name
        self.parameter = parameter
        self.ib = ib
        self.nats = nats
        self.strategy = strategy
        
        self.fills: List[Fill] = []
        
        self.pl = 0.0
        self.position = 0
        
        self.fees = 0.0
        self.buyFees = 0.0
        self.sellFees = 0.0
        
        self.avgBuyPrice = 0.0
        self.avgSellPrice = 0.0
        
        self.buyNotional = 0.0
        self.sellNotional = 0.0
        self.openNotional = 0.0
        
        
        self.volume = 0.0
        self.buyVolume = 0.0
        self.sellVolume = 0.0
        
        self._markPrice = float('nan')
        
        self._pendingPlacementOrderId: Optional[int] = None
        self._pendingStopOrderId: Optional[int] = None
        self._pendingCloseOrderId: Optional[int] = None
        
        self._placementPermId: Optional[int] = None
        self._stopPermId: Optional[int] = None
        self._closePermId: Optional[int] = None
        
        self._latest_entry: Optional[Fill] = None
        
        
        # Initialize
        self.instDef: Instrument = get_definition(name)
        self.contractDetails: Optional[ContractDetails] = None
        self.tradingHours: Optional[TradingHours] = None
        
        # order attempts
        self._initiate_attempts = 0
        self._stopadjtrail_attempts = 0
        self._close_attempts = 0
        
        # flags
        self._can_trade = True
        self._can_initiate = True
        
        self._initialized = False
        
        log.info(f"[{self.name}] Created contract: {self.name}")
        
        
    def initialize(self):
        # self.calc_pl()
        self.initialize_contract_details()
        self.load_state()
        self.initialized = True
        
        
        

    ##### State Management #####
    @property
    def state_key(self) -> str:
        return f"state.contracts.{self.name}"

    def load_state(self):
        state = {}
        try:
            state = self.nats.nats.get_kv(BUCKET, self.state_key)
            log.info(f"[{self.name}] Loading state for {self.name}: {state}")
        except Exception as e:
            log.warning(f"Unable to load state for {self.name}: {e}")
        
        if state:
            markPrice = state.get('markPrice', float('nan'))
            if markPrice is not None:
                self.markPrice = markPrice
            
            # order attempts
            self.initiate_attempts = state.get('initiate_attempts', 0)
            self.stopadjtrail_attempts = state.get('stopadjtrail_attempts', 0) 
            self.close_attempts = state.get('close_attempts', 0)
            
            # flags
            # self.can_trade = state.get('can_trade', True)
            # self.can_initiate = state.get('can_initiate', True)
            
            
    def save_state(self):
        # TODO - remove once dash doesn't use this
        if self.ib is None:
            return
        state = {
            'markPrice': self.markPrice,
            **self.order_attempts,
            **self.flags
        }
        self.nats.nats.put_kv(BUCKET, self.state_key, state)

        
        
    def cleanup(self):
        log.info(f"[{self.name}] Cleaning up contract: {self.name}")
        self.save_state()
        
        
    def rollTradeDate(self):
        log.info(f"[{self.name}] rolling tradeDate - {self.name}")
        self.reset_order_attempts()
        
        
    def update(self):
        if not self.initialized:
            return
        
        endprd = datetime.now()
        bgnprd = datetime.now() - timedelta(minutes=1)
        
        self.check_order_states()
        
        try:
            bars = get_bars_in_period(self.name, 60, bgnprd, endprd)
            # log.debug(f"Downloaded bars: {bars}")
            if len(bars) > 0 and bars[-1].close is not None:
                markPrice = bars[-1].close
                if not math.isnan(markPrice) and markPrice is not None:
                    self.markPrice = bars[-1].close
        except Exception as e:
            log.warning(f"{self.name} unable to download bars: {e}")


    def initialize_contract_details(self):
        if self.ib is not None:
            try:
                self.contractDetails = self.ib.registerContract(self.name, self.instDef.conId, self.instDef.exchange)
                self.tradingHours = TradingHours(self.contractDetails.tradingHours, self.contractDetails.liquidHours, self.contractDetails.timeZoneId)
                log.info(f"[{self.name}] Initialized contract details: {self.contractDetails}")
            except Exception as e:
                log.error(f"Unable to register contract: {e}")
                sys.exit(1)


    def initiate_position(self, side: Side, qty: float):
        """
        Args:
            side (Side): _description_
            size (int): _description_
            stop (Optional[float], optional): _description_. Defaults to None.
            stopAdj (Optional[float], optional): _description_. Defaults to None.
            trail (Optional[float], optional): _description_. Defaults to None.
        """
        
        # check if ib is connected
        if not self.ib:
            log.warning(f"IB not connected, cannot initiate position for {self.name}")
            return
        
        self.check_order_states()
        
        if not self.can_initiate or not self.can_trade:
            log.warning(f"Cannot initiate position for {self.name}. can_initiate: {self.can_initiate}, can_trade: {self.can_trade}")
            return
        
        if self.initiate_attempts == MAX_INITIATE_ATTEMPTS:
            log.warning(f"Max initiate attempts reached for {self.name}")
            self.initiate_attempts += 1
            return
        elif self.initiate_attempts > MAX_INITIATE_ATTEMPTS:
            self.initiate_attempts += 1
            return
        
        if self.pendingPlacementOrderId is not None:
            log.debug(f"placementOrderId is still pending: {self.pendingPlacementOrderId}")
            return
        
        if self.placementPermId is not None and self.placementPermId in self.openOrders:
            log.debug(f"placementOrderId is still open: {self.placementPermId}")
            return
        
        if not self.tradingHours.is_trading:
            return
        
        orderRef = OrderRefLabel.from_instDef(self.strategy, self.instDef, OrderReason.ENTRY) 
        pendingOrderId, placementOrderRef = self.ib.placeAdaptiveMarket(self.instDef.conId, side, qty, orderRef.string)
        self.pendingPlacementOrderId = pendingOrderId
        self.initiate_attempts += 1
        
        log.info(f"[{self.name}] Placed adaptive market order: orderId: {pendingOrderId} - {side} {qty}, orderRef: {placementOrderRef}")



    def placeStopAdjTrail(self, side: Side, qty: float, stop_price: float, trigger_price: float, trail_amount: float):
        
        if not self.ib:
            return
        
        self.check_order_states()
        
        if self.stopadjtrail_attempts >= MAX_STOPADJTRAIL_ATTEMPTS:
            log.warning(f"Max stopadjtrail attempts reached for {self.name}")
            return
        
        if not self.can_trade:
            return
        
        if self.pendingStopOrderId is not None:
            log.debug(f"stopOrderId is still pending: {self.pendingStopOrderId}")
            return
        
        if self.stopPermId is not None and self.stopPermId in self.openOrders:
            return
        
        if not self.tradingHours.is_trading:
            return
        
        orderRef = OrderRefLabel.from_instDef(self.strategy, self.instDef, OrderReason.STOP) 
        orderId, placementOrderRef = self.ib.placeStopAdjTrailOrder(self.instDef.conId, side, qty, stop_price, trigger_price, trail_amount, orderRef.string)
        self.stopPermId = orderId
        self.stopadjtrail_attempts += 1
        
        log.info(f"[{self.name}] Placed stop order orderId:{orderId}, orderRef: {placementOrderRef} - side {side}, qty: {qty}, stop_price: {stop_price}, trigger_price: {trigger_price}, trail_amount: {trail_amount}")
        


    def close_position(self, orderReason: OrderReason):
        
        if not self.ib:
            return
        
        self.check_order_states()
        
        if self.close_attempts >= MAX_CLOSE_ATTEMPTS:
            log.warning(f"Max close attempts reached for {self.name}")
            return
        
        if not self.can_trade:
            return
        
        if self.position == 0:
            return
        
        if not self.tradingHours.is_trading:
            return
        
        if self.pendingCloseOrderId is not None:
            log.debug(f"closeOrderId is still pending: {self.pendingCloseOrderId}")
            return
        
        if self.closePermId is not None and self.closePermId in self.openOrders:
            return
        
        #cancel working entry and stop
        self.cancelWorkingEntry()
        self.cancelWorkingStop()
        
        if not self.tradingHours.is_trading:
            return
        
        log.info(f"[{self.name}] CLOSING POSITION - {self.name}: position: {self.position}, orderReason: {orderReason.value}")
        
        side = Side.BUY if self.position < 0 else Side.SELL
        size = abs(self.position)
        
        orderRef = OrderRefLabel.from_instDef(self.strategy, self.instDef, orderReason) 
        orderId, placementOrderRef = self.ib.placeMarketOrder(self.instDef.conId, side, size, None, orderRef.string)
        self.pendingCloseOrderId = orderId
        self.close_attempts += 1
        
        log.info(f"[{self.name}] Placed market order: orderId: {orderId}, orderRef: {placementOrderRef} - side: {side}, qty: {size}")



    def cancelAllOpenOrders(self):
        if self.ib is not None:
            log.info(f"[{self.name}] Canceling all open orders for {self.name}")
            self.ib.cancelAllOpenOrders()
        
                
                
    def cancelWorkingEntry(self):
        if self.ib is not None:
            for permId, order in self.openOrders.items():
                orderId = order.orderId
                if OrderRefLabel.from_orderRef(order.orderRef).reason == OrderReason.ENTRY:
                    log.info(f"[{self.name}] Cancelling entry order: {orderId}")
                    self.ib.cancelOrder(orderId)
            else:
                pass
        
        
        
    def cancelWorkingStop(self):
        if self.ib is not None:
            for permId, order in self.openOrders.items():
                orderId = order.orderId
                if OrderRefLabel.from_orderRef(order.orderRef).reason == OrderReason.STOP:
                    log.info(f"[{self.name}] Cancelling stop order: {orderId}")
                    self.ib.cancelOrder(orderId)
                    
                    
        
    def process_fill(self, fill: Fill):
        
        self.fills.append(fill)
        log.info(f"[{self.name}] Added fill: {fill}")
        
        self._markPrice = fill.price
        weighted = fill.qty * fill.price
    
        if  fill.side == Side.BUY:
            self.position += fill.qty
            self.buyNotional += weighted * self.pointValue
            self.buyVolume += fill.qty
            self.avgBuyPrice = round(weighted / self.buyVolume, 8)
            self.buyFees += fill.fees
        else:
            self.position -= fill.qty
            self.sellNotional += weighted * self.pointValue
            self.sellVolume += fill.qty
            self.avgSellPrice = round(weighted / self.sellVolume, 8)
            self.sellFees += fill.fees
            
        self.volume += fill.qty
        self.fees += fill.fees
        self.openNotional = self.position * self._markPrice * self.pointValue
        
        if fill.reason == OrderReason.ENTRY and fill.status == FillStatus.COMPLETE:
            self.latest_entry = fill
            log.info(f"[{self.name}] Set current entry: {fill.short_description}")
        
        self.calc_pl()
        
    
    
    def calc_pl(self):
        self.openNotional = self.position * self._markPrice * self.pointValue
        self.pl = round((self.sellNotional - self.buyNotional) + self.openNotional, 2) 
        
        
    def check_order_states(self):
        for permId, order in self.openOrders.items():
            if order.orderId == self.pendingPlacementOrderId:
                self.placementPermId = permId
                self.pendingPlacementOrderId = None
                log.debug(f"pendingPlacementOrderId {self.pendingPlacementOrderId} matched orderId {order.orderId} - setting placementPermId to {permId}")
            if order.orderId == self.pendingStopOrderId:
                self.stopPermId = permId
                self.pendingStopOrderId = None
                log.debug(f"pendingStopOrderId {self.pendingStopOrderId} matched orderId {order.orderId} - setting stopPermId to {permId}")
            if order.orderId == self.pendingCloseOrderId:
                self.closePermId = permId
                self.pendingCloseOrderId = None
                log.debug(f"pendingCloseOrderId {self.pendingCloseOrderId} matched orderId {order.orderId} - setting closePermId to {permId}")
                
        if self.placementPermId is not None and self.placementPermId not in self.openOrders:
            self.placementPermId = None
        if self.stopPermId is not None and self.stopPermId not in self.openOrders:
            self.stopPermId = None
        if self.closePermId is not None and self.closePermId not in self.openOrders:
            self.closePermId = None
    


    @property
    def can_trade(self) -> bool:
        return self._can_trade
    @can_trade.setter
    def can_trade(self, value: bool):
        self._can_trade = value
        self.save_state()
        log.info(f"[{self.name}] can_trade set to {value} for {self.name}")
        
        
    
    @property
    def can_initiate(self) -> bool:
        return self._can_initiate
    @can_initiate.setter
    def can_initiate(self, value: bool):
        self._can_initiate = value
        self.save_state()
        log.info(f"[{self.name}] can_initiate set to {value} for {self.name}")
        
        
        
    @property
    def order_attempts(self) -> Dict[str, int]:
        return {
            'initiate_attempts': self.initiate_attempts,
            'stopadjtrail_attempts': self.stopadjtrail_attempts,
            'close_attempts': self.close_attempts
        }
        
    def reset_order_attempts(self):
        log.info(f"[{self.name}] Resetting order attempts for {self.name}")
        self.initiate_attempts = 0
        self.stopadjtrail_attempts = 0
        self.close_attempts = 0
        self.save_state()
        
        
    @property
    def flags(self) -> Dict[str, bool]:
        return {
            'can_trade': self.can_trade,
            'can_initiate': self.can_initiate
        }

    

    @property
    def markPrice(self) -> float:
        return self._markPrice     
    @markPrice.setter
    def markPrice(self, value: float):
        self._markPrice = value
        self.calc_pl()
    
    @property
    def minTick(self) -> float:
        return float(self.instDef.minTick)
    
    @property
    def pointValue(self) -> float:
        return float(self.instDef.pointValue)
    
    @property
    def tickValue(self) -> float:
        return float(self.instDef.tickValue)
    
    
    @property
    def rollDate(self) -> date:
        rd = self.instDef.contractMonth + timedelta(days=self.parameter.shiftdays)
        while rd.weekday() >= 5:
            rd += timedelta(days=1)
        return rd

    @property
    def daysToRoll(self) -> int:
        return (self.rollDate - self.parameter.get_tradeDate(datetime.now())).days


    @property
    def openOrders(self) -> Dict[int, Order]:
        """
        Returns a dictionary of open orders keyed by permId for this contract.

        Returns:
            Dict[int, Order]: Dictionary of open orders keyed by permId.
        """
        if self.ib is not None:
            return {permId: order for permId, order in self.ib.openOrders.items() if self.ib.permId2conId[permId] == self.instDef.conId}
        return {}

    @property
    def workingEntryExists(self) -> bool:
        return any(OrderRefLabel.from_orderRef(order.orderRef).reason == OrderReason.ENTRY for order in self.openOrders.values())
    
    @property
    def workingStopExists(self) -> bool:
        return any(OrderRefLabel.from_orderRef(order.orderRef).reason == OrderReason.STOP for order in self.openOrders.values())
    
    @property
    def latest_entry(self) -> Optional[Fill]:
        return self._latest_entry
    @latest_entry.setter
    def latest_entry(self, value: Optional[Fill]):
        self._latest_entry = value


    @property
    def initialized(self) -> bool:
        return self._initialized
    
    @initialized.setter
    def initialized(self, value: bool):
        if value != self._initialized:
            log.info(f"[{self.name}] Contract {self.name} initialized: {value}")
        self._initialized = value


    ##### State Management #####
    
    @property
    def initiate_attempts(self) -> int:
        return self._initiate_attempts
    @initiate_attempts.setter
    def initiate_attempts(self, value: int):
        log.debug(f"[{self.name}] initiate_attempts set to {value} from {self._initiate_attempts} for {self.name}")
        self._initiate_attempts = value
        self.save_state()
        
        
    @property
    def stopadjtrail_attempts(self) -> int:
        return self._stopadjtrail_attempts
    @stopadjtrail_attempts.setter
    def stopadjtrail_attempts(self, value: int):
        self._stopadjtrail_attempts = value
        self.save_state()
        
        
    @property
    def close_attempts(self) -> int:
        return self._close_attempts
    @close_attempts.setter
    def close_attempts(self, value: int):
        self._close_attempts = value
        self.save_state()


    @property
    def pendingPlacementOrderId(self) -> Optional[int]:
        return self._pendingPlacementOrderId
    @pendingPlacementOrderId.setter
    def pendingPlacementOrderId(self, value: Optional[int]):
        log.info(f"[{self.name}] pendingPlacementOrderId set to {value} for {self.name}")
        self._pendingPlacementOrderId = value
        self.save_state()

    @property
    def pendingStopOrderId(self) -> Optional[int]:
        return self._pendingStopOrderId
    @pendingStopOrderId.setter
    def pendingStopOrderId(self, value: Optional[int]):
        log.info(f"[{self.name}] pendingStopOrderId set to {value} for {self.name}")
        self._pendingStopOrderId = value
        self.save_state()
        
    @property
    def pendingCloseOrderId(self) -> Optional[int]:
        return self._pendingCloseOrderId
    @pendingCloseOrderId.setter
    def pendingCloseOrderId(self, value: Optional[int]):
        log.info(f"[{self.name}] pendingCloseOrderId set to {value} for {self.name}")
        self._pendingCloseOrderId = value
        self.save_state()
        
        
    @property
    def placementPermId(self) -> Optional[int]:
        return self._placementPermId
    @placementPermId.setter
    def placementPermId(self, value: Optional[int]):
        log.info(f"[{self.name}] placementPermId set to {value} for {self.name}")
        self._placementPermId = value
        self.save_state()
        
    @property
    def stopPermId(self) -> Optional[int]:
        return self._stopPermId
    @stopPermId.setter
    def stopPermId(self, value: Optional[int]):
        log.info(f"[{self.name}] stopPermId set to {value} for {self.name}")
        self._stopPermId = value
        self.save_state()
        
    @property
    def closePermId(self) -> Optional[int]:
        return self._closePermId
    @closePermId.setter
    def closePermId(self, value: Optional[int]):
        log.info(f"[{self.name}] closePermId set to {value} for {self.name}")
        self._closePermId = value
        self.save_state()


    ##### Reporting #####
    
    def summary(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'position': self.position,
            'openOrders': len(self.openOrders),
            'pl': self.pl,
            'daysToRoll': self.daysToRoll,
            'fees': self.fees,
            'markPrice': self.markPrice,
            'avgBuyPrice': self.avgBuyPrice,
            'avgSellPrice': self.avgSellPrice,
            'buyNotional': self.buyNotional,
            'sellNotional': self.sellNotional,
            'openNotional': self.openNotional,
            'rollDate': self.rollDate.strftime("%Y-%m-%d"),
            'buyFees': self.buyFees,
            'sellFees': self.sellFees,
            'volume': self.volume,
            'buyVolume': self.buyVolume,
            'sellVolume': self.sellVolume,
        }


    def __str__(self) -> str:
        return f"Contract(name='{self.name}', position={self.position}, pl={self.pl})"

    def __repr__(self) -> str:
        return (
            f"Contract(name='{self.name}', position={self.position}, pl={self.pl}"
            f", minTick={self.minTick}, pointValue={self.pointValue}, tickValue={self.tickValue}"
            f", markPrice={self.markPrice}, buyNotional={self.buyNotional}, sellNotional={self.sellNotional}" 
            f", fees={self.fees}, buyFees={self.buyFees}, sellFees={self.sellFees}"
            f", volume={self.volume}, buyVolume={self.buyVolume}, sellVolume={self.sellVolume}"
            f", workingEntryExists={self.workingEntryExists}, workingStopExists={self.workingStopExists}"
            f", placementPermId={self.placementPermId}, stopPermId={self.stopPermId}"
            f")"
        )
