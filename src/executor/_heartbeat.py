__all__ = ["Heartbeat"]

import logging
from datetime import datetime
from zoneinfo import ZoneInfo
from threading import Timer

from tradertools.clients import NatsClient, RedisClient


log = logging.getLogger(__name__)

KEY = "heartbeats"

class Heartbeat:
    def __init__(self, nats_client: NatsClient, redis_client: RedisClient, name: str, interval=10):
        self.nats_client = nats_client
        self.redis_client = redis_client
        self.name = name
        self.interval = interval
        self.timer = None
        self.running = False
        self.check_and_create_bucket()
        
        
    
    
    
    def _send_heartbeat(self):
        if self.running:
            
            timestamp = datetime.now(tz=ZoneInfo("America/New_York")).replace(microsecond=0).isoformat()
            
            try:
                self.redis_client.hset(KEY, self.name, timestamp)
            except Exception as e:
                log.error(f"Failed to set redis heartbeat: {e}")
            
            
            try:
                # Check if NATS client is still connected
                if hasattr(self.nats_client, 'is_connected') and not self.nats_client.is_connected():
                    log.warning("NATS client disconnected, stopping heartbeat")
                    self.stop()
                
                self.nats_client.put_kv(KEY, self.name, timestamp)
                                       
                # log.debug("Heartbeat sent")
            except Exception as e:
                log.error(f"Failed to send heartbeat: {e}")
                # Stop heartbeat on connection errors
                if "not running" in str(e).lower() or "disconnected" in str(e).lower():
                    self.stop()
            
            # Schedule next heartbeat only if still running
            if self.running:
                self.timer = Timer(self.interval, self._send_heartbeat)
                self.timer.start()
    
    
    
    def start(self):
        if not self.running:
            self.running = True
            self._send_heartbeat()
    
    
    
    def stop(self):
        self.running = False
        if self.timer:
            self.timer.cancel()
            self.timer = None
            
            
            
    def check_and_create_bucket(self):
        try:
            self.nats_client.get_kv('heartbeats', 'hmmer_executor')
        except Exception as e:
            log.info(f"Creating heartbeats bucket: {e}")
            self.nats_client.create_kv_bucket('heartbeats')