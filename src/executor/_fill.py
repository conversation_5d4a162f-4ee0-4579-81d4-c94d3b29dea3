import os
from dataclasses import dataclass, field
from functools import cache
from typing import Optional, Dict, Any
from datetime import datetime, date

from ibapi.contract import Contract
from ibapi.order import Order
from ibapi.execution import Execution
from ibapi.commission_and_fees_report import CommissionAndFeesReport

from tradertools.instruments import Instrument
from tradertools.clients import get_pg_client

from .types.enums._enums import ExecSide, Side, FillStatus, OrderReason
from .types.constants import TZ

PG_CLIENT = os.getenv("POSTGRES_CLIENT")

@dataclass
class Fill:
    """
    Fill class using composition to combine Execution and CommissionAndFeesReport.
    Maps directly to PostgreSQL table structure.
    """
    # Core fill data (maps to table columns)
    dt: datetime
    tradeDate: date
    prd: str  # product/symbol
    name2: str  # contract description
    price: float
    qty: float
    side: Side  # BUY/SELL
    fees: float # total fees
    reason: OrderReason  # execution reason/source
    orderType: str  # order type (LMT, MKT, etc.)
    orderRef: str  # order reference
    
    status: FillStatus
    
    # Composed objects (stored in raw_data as JSON)
    order: Order
    execution: Execution
    commission_report: CommissionAndFeesReport
    
    # Additional metadata
    raw_data: Optional[Dict[str, Any]] = field(default_factory=dict)
    

    
    
    @classmethod
    def from_ib_objects(cls, contract: Contract, order: Order, execution: Execution, commission_report: CommissionAndFeesReport, 
                       trade_date: Optional[date] = None) -> 'Fill':
        """
        Create a Fill from IB API objects.
        
        Args:
            execution: IB Execution object
            commission_report: IB CommissionAndFeesReport object
            trade_date: Trade date (defaults to today)
            reason: Reason for the fill
        """
        # Extract core data from execution
        tradeDate = datetime.now().astimezone(TZ).date()  
        if trade_date is not None:
            tradeDate = trade_date
            
        orderType = order.orderType
        orderRef = execution.orderRef
        if orderRef is None:
            reason = OrderReason.UNKNOWN
        else:
            val = orderRef.split(".")[-1]
            try:
                reason = OrderReason(val)
            except ValueError:
                reason = OrderReason.UNKNOWN
            
        
        if execution.cumQty == order.totalQuantity:
            status = FillStatus.COMPLETE
        elif execution.cumQty < order.totalQuantity:
            status = FillStatus.PARTIAL
        else:
            status = FillStatus.UKNOWN
            
        instdef = get_definition(contract.conId)
        # Map IB execution data to our fields
        prd = instdef.product
        name2 = instdef.name2
        price = float(execution.price)
        qty = float(execution.shares)
        side = Side(ExecSide(execution.side).name)  # Should be "BOT" or "SLD" from IB
        
        return cls(
            dt=datetime.now().astimezone(TZ),
            tradeDate=tradeDate,
            prd=prd,
            name2=name2,
            price=price,
            qty=qty,
            side=side,
            fees=-commission_report.commissionAndFees,
            reason=reason,
            orderType=orderType,
            orderRef=orderRef,
            status=status,
            order=order,
            execution=execution,
            commission_report=commission_report
        )
        
        
    def summary(self) -> Dict[str, Any]:
        return {
            'datetime': self.dt.astimezone(TZ).strftime("%Y-%m-%d %H:%M %Z"),
            'name2': self.name2,
            'side': self.side.value,
            'qty': self.qty,
            'price': self.price,
            'fees': self.fees,
            'reason': self.reason.value,
            'orderType': self.orderType,
            'orderRef': self.orderRef,
        }
    
    def to_db_dict(self) -> Dict[str, Any]:
        """
        Convert Fill to dictionary for database insertion.
        """
        from ibapi_helpers.serialization import ib_serialize  
        # Serialize only the IB objects for raw_data
        raw_data = ib_serialize({
            'order': self.order,
            'execution': self.execution,
            'commission_report': self.commission_report
        })
        
        return {
            'dt': self.dt,
            'TradeDate': self.tradeDate,
            'prd': self.prd,
            'name2': self.name2,
            'price': self.price,
            'qty': self.qty,
            'side': self.side.value,
            'fees': self.fees,
            'reason': self.reason.value,
            'orderType': self.orderType,
            'orderRef': self.orderRef,
            'status': self.status.value,
            'raw_data': raw_data
        }
    
    @classmethod
    def from_db_dict(cls, db_dict: Dict[str, Any]) -> 'Fill':
        """
        Create Fill from database row dictionary.
        """
        from ibapi_helpers.serialization import ib_deserialize  # Assuming you're using the serializer we built
        
        # Deserialize raw_data back to objects
        order: Order = ib_deserialize(db_dict['raw_data']['order']) if 'order' in db_dict['raw_data'] and db_dict['raw_data']['order'] is not None else None # type: ignore
        execution: Execution = ib_deserialize(db_dict['raw_data']['execution']) if  'execution' in db_dict['raw_data'] and db_dict['raw_data']['execution'] is not None else None # type: ignore
        commission_report: CommissionAndFeesReport = ib_deserialize(db_dict['raw_data']['commission_report']) if 'commission_report' in db_dict['raw_data'] and db_dict['raw_data']['commission_report'] is not None else None # type: ignore
        
        raw_data = {
            'order': order,
            'execution': execution,
            'commission_report': commission_report
        }
        
        return cls(
            dt=db_dict['dt'],
            tradeDate=db_dict['TradeDate'],
            prd=db_dict['prd'],
            name2=db_dict['name2'],
            price=db_dict['price'],
            qty=db_dict['qty'],
            side=Side(db_dict['side']),
            reason=OrderReason(db_dict['reason']),
            fees=db_dict['fees'],
            orderType=db_dict['orderType'],
            orderRef=db_dict['orderRef'],
            status=FillStatus(db_dict['status']),
            order=order,
            execution=execution,
            commission_report=commission_report,
            raw_data=raw_data
        )
    


    def write_to_db(self):
        pg = get_pg_client(PG_CLIENT)
        try:
            pg.insert("hmmer.fills", self.to_db_dict())
            print(f"Recorded fill: {self}")
        except Exception as e:
            print(f"Error recording fill: {self.execId} - {e}")
            
            
            
    # Convenience properties to access IB object attributes
    @property
    def execId(self) -> str:
        """Get execution ID from IB execution object"""
        return self.execution.execId
    
    @property
    def orderId(self) -> int:
        """Get order ID from IB execution object"""
        return self.execution.orderId
    
    @property
    def currency(self) -> str:
        """Get currency from IB commission report"""
        return self.commission_report.currency
    
    
    @property
    def short_description(self) -> str:
        return f"{self.name2} {self.side.value} {self.qty}@{self.price} on {self.dt}"
    
    def __str__(self) -> str:
        return (f"Fill({self.name2} {self.side.value} {self.qty}@{self.price} "
                f"on {self.dt} - status={self.status.value}, exec_id={self.execId}, order_id={self.orderId}"
                f", orderRef={self.orderRef}, orderType={self.orderType})")
    
  

@cache
def get_definition(conId: int) -> Instrument:
    query = f"SELECT * FROM instruments.definitions WHERE \"conId\" = {conId}"
    res = get_pg_client(PG_CLIENT).query(query)
    if len(res) == 0:
        raise ValueError(f"Contract not found: {conId}")
    return Instrument(**res[0])
 
 
