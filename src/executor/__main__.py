


if __name__ == "__main__":
    import os
    import sys
    import logging
    import logging.handlers
    from datetime import datetime
    from pathlib import Path
    import setproctitle
    
    TITLE = "hmmer_executor"
    
    setproctitle.setproctitle(TITLE)
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    
    # Handler 1: Current run log (truncates on each startup)
    current_run_handler = logging.FileHandler(
        filename=logs_dir / f"{TITLE}.log",
        mode='w',  # Overwrites file on each startup
        encoding='utf-8'
    )

    # Handler 2: Daily log with date (appends throughout the day)
    today_date = datetime.now().strftime("%Y%m%d")
    daily_handler = logging.FileHandler(
        filename=logs_dir / f"{TITLE}_{today_date}.log",
        mode='a',  # Appends to existing file
        encoding='utf-8'
    )

    LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG").upper()

    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL, logging.DEBUG),
        format='%(asctime)s.%(msecs)03d | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler(sys.stdout),
            current_run_handler,  # Fresh file each run
            daily_handler,        # Accumulates all runs for the day
        ]
    )
    log = logging.getLogger(__name__)
    
    logging.getLogger("ibapi").setLevel(logging.WARNING)
    logging.getLogger("tradertools").setLevel(logging.INFO)
    logging.getLogger("asyncio").setLevel(logging.INFO)
    
    ENV = os.getenv("ENV")
    if ENV is None:
        raise ValueError("ENV environment variable not set")
    
    pg_client = os.getenv("POSTGRES_CLIENT")
    if pg_client is None:
        raise ValueError("POSTGRES_CLIENT environment variable not set")
    
    import time
    log.info(f"System timezone: {time.tzname}, UTC offset: {time.timezone}")
    
    from executor.main import main
    main()
