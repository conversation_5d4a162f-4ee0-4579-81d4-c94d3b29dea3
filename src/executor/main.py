import os
import sys

import signal
import logging

import yaml
# import asyncio
from threading import Thread, Event

from tradertools.clients import get_redis_client

from ._heartbeat import Heartbeat
from ._natsinterface import NatsInterface
from .ibclient import IBClient
from ._portfolio import Portfolio
from ._teams import post_to_teams

log = logging.getLogger(__name__)

# ignore iba<PERSON> logging
LOOP_INTERVAL = 15  # seconds
NOTIFY_EXIT_CODES = {1, 2, 126, 127, 128, 129, 131, 132, 134, 137, 139}



NATS_CLIENT = os.getenv("NATS_CLIENT")
if NATS_CLIENT is None:
    raise ValueError("NATS_CLIENT environment variable not set")

MD_CLIENT = os.getenv("MD_CLIENT")
if MD_CLIENT is None:
    raise ValueError("MD_CLIENT environment variable not set")

REDIS_CLIENT= os.getenv("REDIS_CLIENT")
if REDIS_CLIENT is None:
    raise ValueError("REDIS_CLIENT environment variable not set")





def handle_exception(exc_type, exc_value, exc_traceback):
    """Handle unhandled exceptions"""
    if issubclass(exc_type, KeyboardInterrupt):
        # Let KeyboardInterrupt be handled by signal handler
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # log.error(f"Unhandled exception: {exc_type.__name__}: {exc_value}", 
    #             exc_info=(exc_type, exc_value, exc_traceback))
    log.exception(f"Unhandled exception: {exc_type.__name__}: {exc_value}")
    # Convert exception details to JSON-serializable format
    try:
        post_to_teams(
            f"Hmmer_executor: Unhandled exception: {exc_type.__name__}: {str(exc_value)}", 
                details={
                    "exc_type": exc_type.__name__, 
                    "exc_value": str(exc_value),  # Convert to string
                    "module": getattr(exc_type, '__module__', 'unknown')
                }
        )
    except Exception as teams_error:
        log.error(f"Failed to send Teams notification: {teams_error}")
    sys.exit(1)


    
def create_nats_buckets(nats: NatsInterface):
    nats.nats.create_kv_bucket("hmmer")
    


def main():
    log.info("Starting Executor app")
    exit_event = Event()
    shutdown_initiated = False
    
    sys.excepthook = handle_exception

    def handle_exit(sig: int, frame):
        nonlocal shutdown_initiated, p, heartbeat
        if shutdown_initiated:
            return 
        
        shutdown_initiated = True
        log.info(f"Exiting on signal {sig}")
        heartbeat.stop()
        p.cleanup()
        try:
            if 'ib' in locals() and ib:
                ib.shutdown()
            if 'nats' in locals() and nats:
                nats.disconnect()
        except Exception as e:
            log.error(f"Error during shutdown: {e}")
        finally:
            exit_event.set()
    
    signal.signal(signal.SIGTERM, handle_exit)
    signal.signal(signal.SIGINT, handle_exit)
    
    nats = NatsInterface(NATS_CLIENT)
    create_nats_buckets(nats)
    
    r = get_redis_client(REDIS_CLIENT)
    
    with open("./config/ib.yaml", "r") as f:
        config = yaml.safe_load(f)
        host = config["IP"]
        port = config["PORT"]
        client_id = config["CLIENT_ID"]
    
    # nats.connect()
    ib = IBClient(host, port, client_id)
    ib.connect(host, port, client_id)
    Thread(target=ib.run, daemon=True).start()
        
    
    p = Portfolio(nats, r, ib, True)
    p.initialize()
    
    heartbeat = Heartbeat(nats.nats, r, "hmmer_executor")
    heartbeat.start()

    connection_check_counter = 0

    while not exit_event.is_set():
        p.update(update_bars=True)
        
        connection_check_counter += 1
        if connection_check_counter >= 1: # check every interval
            ib.check_and_reconnect()
            connection_check_counter = 0
        
        exit_event.wait(LOOP_INTERVAL)
    
    
    # exit_event.wait()
    print("Executor app exiting")
    



# async def main():
#     log.info("Starting Executor app")
#     shutdown_event = asyncio.Event()
#     shutdown_initiated = False
    
#     sys.excepthook = handle_exception

#     def handle_exit(sig: int, frame):
#         nonlocal shutdown_initiated
#         if shutdown_initiated:
#             return 
        
#         shutdown_initiated = True
#         log.info(f"Exiting on signal {sig}")
#         shutdown_event.set()
    
#     signal.signal(signal.SIGTERM, handle_exit)
#     signal.signal(signal.SIGINT, handle_exit)
    
#     # Initialize components
#     nats = NatsInterface(NATS_CLIENT)
#     create_nats_buckets(nats)
    
#     with open("./config/ib.yaml", "r") as f:
#         config = yaml.safe_load(f)
#         host = config["IP"]
#         port = config["PORT"]
#         client_id = config["CLIENT_ID"]
    
#     ib = IBClient(host, port, client_id)
#     ib.connect(host, port, client_id)
    
#     # Start IB client in executor to avoid blocking
#     loop = asyncio.get_event_loop()
#     ib_task = loop.run_in_executor(None, ib.run)
    
#     p = Portfolio(nats, ib, True)
#     p.initialize()

#     # Create concurrent tasks
#     portfolio_task = asyncio.create_task(portfolio_update_loop(p, shutdown_event))
#     connection_task = asyncio.create_task(connection_check_loop(ib, shutdown_event))
    
#     try:
#         # Wait for shutdown event first
#         await shutdown_event.wait()
        
#         # Cancel tasks that can be cancelled
#         portfolio_task.cancel()
#         connection_task.cancel()
        
#         # Give tasks a moment to clean up
#         try:
#             await asyncio.wait_for(
#                 asyncio.gather(portfolio_task, connection_task, return_exceptions=True),
#                 timeout=2.0
#             )
#         except asyncio.TimeoutError:
#             log.warning("Tasks didn't complete cleanup within timeout")
            
#     except Exception as e:
#         log.error(f"Error in main loop: {e}")
#     finally:
#         # Cleanup
#         try:
#             p.cleanup()
#         except Exception as e:
#             log.error(f"Error cleaning up portfolio: {e}")
            
#         try:
#             if ib:
#                 ib.shutdown()
#         except Exception as e:
#             log.error(f"Error disconnecting IB: {e}")
            
#         try:
#             if nats:
#                 nats.disconnect()
#         except Exception as e:
#             log.error(f"Error disconnecting NATS: {e}")
        
#         # Cancel the IB task if it's still running
#         if not ib_task.done():
#             ib_task.cancel()
#             try:
#                 await asyncio.wait_for(ib_task, timeout=1.0)
#             except (asyncio.TimeoutError, asyncio.CancelledError):
#                 log.warning("IB task didn't respond to cancellation")
    
#     log.info("Executor app exiting")


# async def portfolio_update_loop(portfolio: Portfolio, shutdown_event: asyncio.Event):
#     """Handle portfolio updates in async loop"""
#     while not shutdown_event.is_set():
#         try:
#             # Run blocking portfolio update in executor
#             loop = asyncio.get_event_loop()
#             await loop.run_in_executor(None, portfolio.update, True)
#         except Exception as e:
#             log.error(f"Error updating portfolio: {e}")
        
#         try:
#             await asyncio.wait_for(shutdown_event.wait(), timeout=LOOP_INTERVAL)
#             break  # Shutdown requested
#         except asyncio.TimeoutError:
#             continue  # Timeout reached, continue loop


# async def connection_check_loop(ib_client: IBClient, shutdown_event: asyncio.Event):
#     """Handle connection checks in async loop"""
#     while not shutdown_event.is_set():
#         try:
#             # Run blocking connection check in executor
#             loop = asyncio.get_event_loop()
#             await loop.run_in_executor(None, ib_client.check_and_reconnect)
#         except Exception as e:
#             log.error(f"Error checking connection: {e}")
        
#         try:
#             await asyncio.wait_for(shutdown_event.wait(), timeout=LOOP_INTERVAL)
#             break  # Shutdown requested
#         except asyncio.TimeoutError:
#             continue  # Timeout reached, continue loop
