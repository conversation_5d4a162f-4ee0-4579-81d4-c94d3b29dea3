__all__ = ['Bar']

from dataclasses import dataclass
from typing import Dict, Any
from datetime import datetime, date
from zoneinfo import ZoneInfo



@dataclass
class Bar:
    dt: datetime
    et: datetime
    tradeDate: date
    prd: str
    name2: str
    expiry: int
    fwdper: int
    open: float
    high: float
    low: float
    close: float
    volume: int
    pridce_fd1: float
    price_fd2: float
    norm_position: float
    return_: float
    regime: int
    written: datetime
    
    
    @classmethod
    def from_db_dict(cls, db_dict: Dict[str, Any]) -> 'Bar':
        """
        Create Bar from database row dictionary.
        """
        return cls(
            dt=db_dict['dt'].astimezone(ZoneInfo("America/New_York")),
            et=db_dict['et'].astimezone(ZoneInfo("America/New_York")),
            tradeDate=db_dict['TradeDate'],
            prd=db_dict['prd'],
            name2=db_dict['name2'],
            expiry=db_dict['expiry'],
            fwdper=db_dict['fwdper'],
            open=db_dict['Open'],
            high=db_dict['High'],
            low=db_dict['Low'],
            close=db_dict['Close'],
            volume=db_dict['Volume'],
            pridce_fd1=db_dict['price_fd1'],
            price_fd2=db_dict['price_fd2'],
            norm_position=db_dict['norm_position'],
            return_=db_dict['return'],
            regime=db_dict['hmm_regime'],
            written=db_dict['written']
        )
        
        
    def __repr__(self) -> str:
        return (
            f"Bar(dt={self.dt}, et={self.et}, tradeDate={self.tradeDate}, prd={self.prd}, name2={self.name2}"
            f", expiry={self.expiry}, fwdper={self.fwdper}, open={self.open}, high={self.high}, low={self.low}, close={self.close}, volume={self.volume}"
            f", pridce_fd1={self.pridce_fd1}, price_fd2={self.price_fd2}, norm_position={self.norm_position}, return_={self.return_}, regime={self.regime}, written={self.written})"
        )


    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Bar):
            return False
        return self.dt == other.dt and self.prd == other.prd and self.name2 == other.name2
