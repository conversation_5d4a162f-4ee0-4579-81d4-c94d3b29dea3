__all__ = [
    "Side",
    "ExecSide",
    "TradingState",
    "OrderReason",
    "FillStatus"
]

from enum import Enum



class Side(Enum):
    BUY = "BUY"
    SELL = "SELL"


class ExecSide(Enum):
    BUY = "BOT"
    SELL = "SLD"


class TradingState(Enum):
    READY = "READY"
    ACTIVE = "ACTIVE"
    STOPPED_OUT = "STOPPED_OUT"
    WAITING = "WAITING"


class OrderReason(Enum):
    ENTRY = "entry"
    STOP = "stop"
    TRAIL = "trail"
    EXIT = "exit"
    ROLL = "roll"
    UNKNOWN = "unknown"


class FillStatus(Enum):
    COMPLETE = "complete"
    PARTIAL = "partial"
    UKNOWN = "unknown"

