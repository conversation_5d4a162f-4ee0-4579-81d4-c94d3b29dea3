__all__ = ["OrderRefLabel"]


from dataclasses import dataclass

from .types.enums import OrderReason
from tradertools.instruments import Instrument


@dataclass
class OrderRefLabel:
    strategy: str
    product: str
    name2: str
    reason: OrderReason
    
    
    @classmethod
    def from_instDef(cls, strategy: str, instDef: Instrument, reason: OrderReason) -> 'OrderRefLabel':
        return cls(strategy, instDef.product, instDef.name2, reason)
    
    
    @classmethod
    def from_orderRef(cls, orderRef: str) -> 'OrderRefLabel':
        strategy, product, name2, reason = orderRef.split(".")
        reason = OrderReason(reason)
        return cls(strategy, product, name2, reason)
    

    @property
    def string(self) -> str:
        return f"{self.strategy}.{self.product}.{self.name2}.{self.reason.value}"

    def __str__(self) -> str:
        return f"{self.strategy}.{self.product}.{self.name2}.{self.reason.value}"
