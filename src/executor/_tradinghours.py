__all__ = ["TradingHours"]

from typing import List, Tuple
from datetime import datetime
from zoneinfo import ZoneInfo




class TradingHours:
    
    def __init__(self, tradingHours: str, liquidHours, tz: str):
        self.tradingHours = tradingHours if tradingHours != '' else None
        self.liquidHours = liquidHours if liquidHours != '' else None
        self.tz = ZoneInfo(tz) if tz != '' else ZoneInfo("America/New_York")
        self.trading_periods = self.parse_hours(self.tradingHours)
        self.liquid_periods = self.parse_hours(self.liquidHours)
        
    def parse_hours(self, hours: str) -> List[Tuple[datetime, datetime]]:
        periods = []
        if hours is None:
            return periods
        
        now = datetime.now(self.tz)
        for p in hours.split(';'):
            if 'CLOSED' in p or p == '':
                continue
            start, end = p.split('-')
            start = datetime.strptime(start, '%Y%m%d:%H%M').replace(tzinfo=self.tz)
            end = datetime.strptime(end, '%Y%m%d:%H%M').replace(tzinfo=self.tz)
            if end > now:
                periods.append((start, end))
        return periods
            
    @property
    def is_trading(self) -> bool:
        now = datetime.now(self.tz)
        for start, end in self.trading_periods:
            if now >= start and now < end:
                return True
        return False
    
    @property
    def is_liquid(self) -> bool:
        now = datetime.now(self.tz)
        for start, end in self.liquid_periods:
            if now >= start and now < end:
                return True
        return False
    
    @property
    def trading_start(self) -> datetime | None:
        if self.is_trading:
            return None
        now = datetime.now(self.tz)
        for start, end in self.trading_periods:
            if end <= now:
                continue
            return start
        return None
    
    @property
    def liquid_start(self) -> datetime | None:
        if self.is_liquid:
            return None
        now = datetime.now(self.tz)
        for start, end in self.liquid_periods:
            if end <= now:
                continue
            return start
        return None
    
    @property
    def trading_end(self) -> datetime | None:
        if not self.is_trading:
            return None
        now = datetime.now(self.tz)
        for _, end in self.trading_periods:
            if end <= now:
                continue
            return end
        return None
    
    @property
    def liquid_end(self) -> datetime | None:
        if not self.is_liquid:
            return None
        now = datetime.now(self.tz)
        for _, end in self.liquid_periods:
            if end <= now:
                continue
            return end
        return None
