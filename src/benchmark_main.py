"""Main module for hmmer."""
import os
import time
import yaml
import logging
from typing import Dict, Any

# from  hmmer.st_mprocess_slowloop import run
from  hmmer.st_mprocess import run
from hmmer.fdff_funcs import funcs

log = logging.getLogger(__name__)


results = {}


def main() -> Dict[str, Dict[str, Any]]:
    """Entry point for the application."""
    global results
    prods = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'ZB', 'KC', 'SB']
    try:
        with open("./config/products.yaml", "r") as f:
            data = yaml.safe_load(f)
            prods = data["products"]
    except Exception as e:
        log.error(f"Error loading products from config: {e}")
        
    prods = ['CL']
    ibarsize = os.getenv("IBARSIZE", 30)
    
    results = {}
    for p in prods: 
        results[p] = []
        for f in funcs:
            log.info(f"Running product: {p}, function: {f}")
            start_time = time.time()
            try:
                results[p].append({
                    'func': f,
                    'results': run(p, int(ibarsize), n_jobs=12, fd_func=funcs[f]),
                    'time': round(time.time() - start_time, 4)
                })
                    # run(p, int(ibarsize), n_jobs=1)
            except Exception as e:
                log.error(f"Error running {p}")
                log.exception(e)
                results[p].append({
                    'func': f,
                    'results': None,
                    'time': round(time.time() - start_time, 4)
                })

    return results












results = main()

import pickle

with open(f"./benchmarks/benchamrk_njob_12_results.pkl", "wb") as f:
    pickle.dump(results, f)

res = {}
res['01'] = pickle.load(open("./benchmarks/benchamrk_njob_01_results.pkl", "rb"))
res['12'] = pickle.load(open("./benchmarks/benchamrk_njob_12_results.pkl", "rb"))
res['16'] = pickle.load(open("./benchmarks/benchamrk_njob_16_results.pkl", "rb"))


for r in res:
    print(r)
    for p in res[r]['CL']:
        try:
            print(f"  {p['func']}: {p['time']}")
        except Exception as e:
            pass
        
for r in res:
    print(r)
    for p in res[r]['CL']:
        try:
            print(f"\n{p['func']}")
            print(p['results']['yearly_summary'])
        except Exception as e:
            pass



# import pickle

# with open(f"./benchmarks/benchamrk_njob_16_results.pkl", "wb") as f:
#     pickle.dump(results, f)

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple

def compare_dataframes_similarity(
    df1: pd.DataFrame, 
    df2: pd.DataFrame,
    rtol: float = 1e-5,
    atol: float = 1e-8,
    check_dtype: bool = True,
    check_index: bool = True,
    check_columns: bool = True,
    categorical_threshold: float = 0.95
) -> Dict[str, Any]:
    """
    Compare two DataFrames for similarity with tolerance for numerical differences.
    
    Args:
        df1, df2: DataFrames to compare
        rtol: Relative tolerance for numerical comparison
        atol: Absolute tolerance for numerical comparison
        check_dtype: Whether to check data types
        check_index: Whether to check index equality
        check_columns: Whether to check column names
        categorical_threshold: Threshold for categorical similarity (0-1)
    
    Returns:
        Dict containing similarity metrics and comparison results
    """
    result = {
        'shapes_equal': False,
        'columns_equal': False,
        'index_equal': False,
        'dtypes_equal': False,
        'values_similar': False,
        'similarity_score': 0.0,
        'column_similarities': {},
        'differences': {}
    }
    
    # Check shapes
    result['shapes_equal'] = df1.shape == df2.shape
    if not result['shapes_equal']:
        result['differences']['shape'] = f"df1: {df1.shape}, df2: {df2.shape}"
        return result
    
    # Check columns
    if check_columns:
        result['columns_equal'] = df1.columns.equals(df2.columns)
        if not result['columns_equal']:
            result['differences']['columns'] = {
                'df1_only': set(df1.columns) - set(df2.columns),
                'df2_only': set(df2.columns) - set(df1.columns)
            }
    
    # Check index
    if check_index:
        result['index_equal'] = df1.index.equals(df2.index)
        if not result['index_equal']:
            result['differences']['index'] = "Indices are different"
    
    # Check dtypes
    if check_dtype:
        result['dtypes_equal'] = df1.dtypes.equals(df2.dtypes)
        if not result['dtypes_equal']:
            dtype_diff = {}
            for col in df1.columns:
                if col in df2.columns and df1[col].dtype != df2[col].dtype:
                    dtype_diff[col] = f"df1: {df1[col].dtype}, df2: {df2[col].dtype}"
            result['differences']['dtypes'] = dtype_diff
    
    # Compare values column by column
    similar_columns = 0
    total_similarity = 0.0
    
    for col in df1.columns:
        if col not in df2.columns:
            continue
            
        col_similarity = _compare_column_similarity(
            df1[col], df2[col], rtol, atol, categorical_threshold
        )
        
        result['column_similarities'][col] = col_similarity
        total_similarity += col_similarity['similarity_score']
        
        if col_similarity['similar']:
            similar_columns += 1
    
    # Overall similarity metrics
    if len(df1.columns) > 0:
        result['similarity_score'] = total_similarity / len(df1.columns)
        result['values_similar'] = similar_columns / len(df1.columns) >= 0.8
    
    return result

def _compare_column_similarity(
    s1: pd.Series, 
    s2: pd.Series, 
    rtol: float, 
    atol: float, 
    categorical_threshold: float
) -> Dict[str, Any]:
    """Compare similarity of two Series."""
    
    result = {
        'similar': False,
        'similarity_score': 0.0,
        'method': '',
        'details': {}
    }
    
    # Handle missing values
    if s1.isna().sum() != s2.isna().sum():
        result['details']['na_count_diff'] = abs(s1.isna().sum() - s2.isna().sum())
    
    # Drop NaN values for comparison
    s1_clean = s1.dropna()
    s2_clean = s2.dropna()
    
    if len(s1_clean) == 0 and len(s2_clean) == 0:
        result['similar'] = True
        result['similarity_score'] = 1.0
        result['method'] = 'both_empty'
        return result
    
    # Numerical comparison
    if pd.api.types.is_numeric_dtype(s1) and pd.api.types.is_numeric_dtype(s2):
        try:
            # Use numpy's allclose for numerical comparison
            if len(s1_clean) == len(s2_clean):
                similar = np.allclose(s1_clean.values, s2_clean.values, rtol=rtol, atol=atol)
                if similar:
                    result['similar'] = True
                    result['similarity_score'] = 1.0
                else:
                    # Calculate similarity based on relative differences
                    rel_diff = np.abs((s1_clean.values - s2_clean.values) / (s1_clean.values + 1e-10))
                    result['similarity_score'] = 1.0 - np.mean(rel_diff)
                    result['similar'] = result['similarity_score'] >= 0.95
                
                result['method'] = 'numerical'
                result['details']['max_abs_diff'] = np.max(np.abs(s1_clean.values - s2_clean.values))
                result['details']['mean_rel_diff'] = np.mean(rel_diff) if not similar else 0.0
                
        except Exception as e:
            result['details']['error'] = str(e)
    
    # Categorical/String comparison
    else:
        if len(s1_clean) == len(s2_clean):
            matches = (s1_clean.values == s2_clean.values).sum()
            result['similarity_score'] = matches / len(s1_clean) if len(s1_clean) > 0 else 1.0
            result['similar'] = result['similarity_score'] >= categorical_threshold
            result['method'] = 'categorical'
            result['details']['match_rate'] = result['similarity_score']
    
    return result

orig = res['01']['CL'][0]['results']['yearly_summary']
v2 = res['16']['CL'][2]['results']['yearly_summary']

from pprint import pprint as pp
pp(compare_dataframes_similarity(orig, v2))

