# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version


# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/

# configurations
configs/

# OS specific
.DS_Store
Thumbs.db

# project specific
usr/*
connections.yaml
logs/
*.log


*.pkl
*.csv


zandbox/
data/