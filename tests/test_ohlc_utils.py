import pytest
import logging
import pickle
from pathlib import Path
import pandas as pd

from hmmer.ohlc_utils import calculate_expiry_dates as calculate_expiry_dates_v1
from hmmer.ohlc_utils2 import calculate_expiry_dates as calculate_expiry_dates_v2
from hmmer.ohlc_utils import add_expiry_position as add_expiry_position_v1
from hmmer.ohlc_utils2 import add_expiry_position as  add_expiry_position_v2

# Configure logging
logging.getLogger('tradertools').setLevel(logging.WARNING)
logging.getLogger('joblib').setLevel(logging.WARNING)


# Setup test data paths
TEST_DATA_DIR = Path("./tests/data")

# List of products to test
PRODUCTS = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'KC', 'SB']

CALCULATE_EXPIRY_DATES_PARAMS = {
    'CL': {'start_date': pd.Timestamp('2008-11-04 00:00:00'), 'end_date': pd.Timestamp('2025-03-19 00:00:00'), 'change_day': -15, 'exit_mths': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'num_columns': 20},
    'NG': {'start_date': pd.Timestamp('2008-06-25 00:00:00'), 'end_date': pd.Timestamp('2025-03-19 00:00:00'), 'change_day': -15, 'exit_mths': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'num_columns': 20},
    'ZC': {'start_date': pd.Timestamp('2008-06-19 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [2, 4, 6, 8, 11], 'num_columns': 20},
    'ZS': {'start_date': pd.Timestamp('2008-11-03 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [2, 4, 6, 7, 8, 10, 12], 'num_columns': 20},
    'ZW': {'start_date': pd.Timestamp('2008-11-03 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [2, 4, 6, 8, 11], 'num_columns': 20},
    'HG': {'start_date': pd.Timestamp('2008-11-04 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [2, 4, 6, 8, 11], 'num_columns': 20},
    'GC': {'start_date': pd.Timestamp('2008-05-05 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [1, 3, 5, 7, 9, 11], 'num_columns': 20},
    'NQ': {'start_date': pd.Timestamp('2009-01-21 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': 15, 'exit_mths': [3, 6, 9, 12], 'num_columns': 20},
    'KC': {'start_date': pd.Timestamp('2008-11-06 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [2, 4, 6, 8, 11], 'num_columns': 20},
    'SB': {'start_date': pd.Timestamp('2008-05-30 00:00:00'), 'end_date': pd.Timestamp('2025-03-10 00:00:00'), 'change_day': -5, 'exit_mths': [2, 4, 6, 9], 'num_columns': 20},
}


def test_calculate_expiry_dates():
    """Test that both implementations of calculate_expiry_dates produce identical results"""
    
    # Test parameters
    start_date = pd.Timestamp('2020-01-01')
    end_date = pd.Timestamp('2023-12-31')
    date_range = pd.date_range('2020-01-01', '2023-12-31', freq='D')
    test_cases = [
        {'change_day': 5, 'exit_mths': [3, 6, 9, 12], 'num_columns': 12},
        {'change_day': -10, 'exit_mths': [1, 4, 7, 10], 'num_columns': 6},
        {'change_day': 0, 'exit_mths': [2, 5, 8, 11], 'num_columns': 24},
    ]
    
    for case in test_cases:
        # Get results from both implementations
        result_v1 = calculate_expiry_dates_v1(
            date_range, 
            case['change_day'], 
            case['exit_mths'], 
            case['num_columns']
        )
        result_v2 = calculate_expiry_dates_v2(
            start_date,
            end_date, 
            case['change_day'], 
            case['exit_mths'], 
            case['num_columns']
        )
        
        try:
            # Basic DataFrame comparison
            pd.testing.assert_frame_equal(result_v1, result_v2)
        except AssertionError as e:
            print(f"\nDifferences found for case {case}:")
            print(str(e))
            if isinstance(result_v1, pd.DataFrame) and isinstance(result_v2, pd.DataFrame):
                print("\nShape comparison:")
                print(f"result_v1 shape: {result_v1.shape}")
                print(f"result_v2 shape: {result_v2.shape}")
                print("\nColumn comparison:")
                print(f"result_v1 columns: {result_v1.columns.tolist()}")
                print(f"result_v2 columns: {result_v2.columns.tolist()}")
        
        
@pytest.mark.parametrize("product", PRODUCTS)
def test_calculate_expiry_dates_with_product(product):
    """Test that both implementations of calculate_expiry_dates produce identical results"""
    params = CALCULATE_EXPIRY_DATES_PARAMS[product]
    
    date_range = pd.date_range(params['start_date'], params['end_date'], freq='D')
    result_v1 = calculate_expiry_dates_v1(
        date_range,
        params['change_day'], 
        params['exit_mths'], 
        params['num_columns']
    )
    result_v2 = calculate_expiry_dates_v2(
        params['start_date'],
        params['end_date'], 
        params['change_day'], 
        params['exit_mths'], 
        params['num_columns']
    )
    
    try:
        # Basic DataFrame comparison
        pd.testing.assert_frame_equal(result_v1, result_v2)
    except AssertionError as e:
        print(f"\nDifferences found for product {product}:")
        print(str(e))
        if isinstance(result_v1, pd.DataFrame) and isinstance(result_v2, pd.DataFrame):
            print("\nShape comparison:")
            print(f"result_v1 shape: {result_v1.shape}")
            print(f"result_v2 shape: {result_v2.shape}")
            print("\nColumn comparison:")
            print(f"result_v1 columns: {result_v1.columns.tolist()}")
            print(f"result_v2 columns: {result_v2.columns.tolist()}")


    

def test_add_expiry_positions():
    """Test that both implementations of add_expiry_position produce identical results"""
    
    # Create sample test data
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
    n_dates = len(dates)
    
    # Create repeating month sequence of exact length
    months = list(range(1, 13))  # [1,2,3,...,12]
    n_repeats = (n_dates + 11) // 12  # ceiling division to ensure we have enough months
    exp_mnum = (months * n_repeats)[:n_dates]  # repeat and trim to exact length
    
    test_data = pd.DataFrame({
        'Date': dates,
        'Product': 'CL',
        'exp_yr': 2024,
        'exp_mnum': exp_mnum,
    })
    
    # Test parameters - CL has shiftdays of -15
    shiftdays = -15
    products_with_change_days = [('CL', shiftdays)]
    
    # Get results from both implementations
    result_v1 = add_expiry_position_v1(
        test_data,
        products_with_change_days,
        trade_date_col='Date',
        product_col='Product',
        trade_year_col='exp_yr',
        trade_month_col='exp_mnum'
    )
    
    result_v2 = add_expiry_position_v2(
        test_data,
        shiftdays,
        trade_date_col='Date',
        trade_year_col='exp_yr',
        trade_month_col='exp_mnum'
    )
    
    try:
        # Ensure both results have the same columns
        assert set(result_v1.columns) == set(result_v2.columns), \
            f"Column mismatch: v1={result_v1.columns.tolist()}, v2={result_v2.columns.tolist()}"
        
        # Sort columns to ensure same order
        result_v1 = result_v1.sort_index(axis=1)
        result_v2 = result_v2.sort_index(axis=1)
        
        # Compare DataFrames
        pd.testing.assert_frame_equal(result_v1, result_v2)
    except AssertionError as e:
        print("\nDifferences found:")
        print(str(e))
        print("\nShape comparison:")
        print(f"result_v1 shape: {result_v1.shape}")
        print(f"result_v2 shape: {result_v2.shape}")
        print("\nColumn comparison:")
        print(f"result_v1 columns: {result_v1.columns.tolist()}")
        print(f"result_v2 columns: {result_v2.columns.tolist()}")
        raise e


@pytest.mark.parametrize("product", PRODUCTS)
def test_add_expiry_positions_with_product(product):
    """Test that both implementations of add_expiry_position produce identical results"""
    print()
    # Create sample test data
    params = CALCULATE_EXPIRY_DATES_PARAMS[product]
    shiftdays = params['change_day']
    with open(f"{TEST_DATA_DIR}/idata_{product}.pkl", "rb") as f:
        idata = pickle.load(f)
    
    products_with_change_days = [(product, shiftdays)]
    
    # Get results from both implementations
    result_v1 = add_expiry_position_v1(
        idata,
        products_with_change_days,
        trade_date_col='TradeDate',
        product_col='product',
        trade_year_col='expiry_yr',
        trade_month_col='expiry_mth'
    )
    
    result_v2 = add_expiry_position_v2(
        idata,
        shiftdays,
        trade_date_col='TradeDate',
        trade_year_col='expiry_yr',
        trade_month_col='expiry_mth'
    )
    
    try:
        # Ensure both results have the same columns
        assert set(result_v1.columns) == set(result_v2.columns), \
            f"Column mismatch: v1={result_v1.columns.tolist()}, v2={result_v2.columns.tolist()}"
        
        # Sort columns to ensure same order
        result_v1 = result_v1.sort_index(axis=1)
        result_v2 = result_v2.sort_index(axis=1)
        
        # Compare DataFrames
        pd.testing.assert_frame_equal(result_v1, result_v2)
    except AssertionError as e:
        print("\nDifferences found:")
        print(str(e))
        print("\nShape comparison:")
        print(f"result_v1 shape: {result_v1.shape}")
        print(f"result_v2 shape: {result_v2.shape}")
        print("\nColumn comparison:")
        print(f"result_v1 columns: {result_v1.columns.tolist()}")
        print(f"result_v2 columns: {result_v2.columns.tolist()}")
        raise e