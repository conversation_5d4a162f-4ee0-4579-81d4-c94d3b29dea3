import numpy as np
import time
from typing import Callable
import pytest

# Original version
def smooth_states_weighted_original(states, window=5, alpha=0.7):
    """Original vectorized implementation of weighted smoothing"""
    n = len(states)
    smoothed = np.zeros_like(states)
    max_window = min(window, n)
    weights = np.power(alpha, np.arange(max_window - 1, -1, -1))

    for i in range(0, n, 100):
        end = min(i + 100, n)
        for j in range(i, end):
            window_size = min(max_window, j + 1)
            start = max(0, j - window_size + 1)
            unique_vals, counts = np.unique(states[start:j + 1], return_counts=True)
            actual_weights = weights[-window_size:]
            weighted_counts = np.zeros_like(unique_vals, dtype=float)
            for k, val in enumerate(unique_vals):
                mask = states[start:j + 1] == val
                weighted_counts[k] = np.sum(actual_weights[mask])
            smoothed[j] = unique_vals[np.argmax(weighted_counts)]
    return smoothed

# Improved version
def smooth_states_weighted_improved(states: np.ndarray, window: int = 5, alpha: float = 0.7) -> np.ndarray:
    """Improved vectorized implementation using convolution"""
    n = len(states)
    smoothed = np.zeros_like(states)
    max_window = min(window, n)
    weights = np.power(alpha, np.arange(max_window - 1, -1, -1))
    
    # Process each position
    for i in range(n):
        window_size = min(max_window, i + 1)
        start = max(0, i - window_size + 1)
        
        # Get unique values and their weighted counts
        unique_vals = np.unique(states[start:i + 1])
        weighted_counts = np.zeros_like(unique_vals, dtype=float)
        
        # Use broadcasting for faster computation
        state_matrix = states[start:i + 1, np.newaxis] == unique_vals
        actual_weights = weights[-window_size:][:, np.newaxis]
        weighted_counts = np.sum(state_matrix * actual_weights, axis=0)
        
        # Assign the state with maximum weighted count
        smoothed[i] = unique_vals[np.argmax(weighted_counts)]
    
    return smoothed

def generate_test_data(size: int, n_states: int = 3) -> np.ndarray:
    """Generate synthetic state sequence data"""
    return np.random.randint(0, n_states, size=size)

def benchmark_function(func: Callable, states: np.ndarray, n_runs: int = 5, **kwargs) -> float:
    """Benchmark a function's execution time"""
    times = []
    for _ in range(n_runs):
        start = time.perf_counter()
        _ = func(states, **kwargs)
        end = time.perf_counter()
        times.append(end - start)
    return np.mean(times)

@pytest.mark.parametrize("size", [1000, 10000, 100000])
# @pytest.mark.parametrize("window", [5, 10, 20])
# @pytest.mark.parametrize("alpha", [0.7, 0.9])
def test_performance_comparison(size: int, window: int=10, alpha: float=0.7):
    """Compare performance of original and improved implementations"""
    # Generate test data
    states = generate_test_data(size)
    
    # Benchmark both implementations
    original_time = benchmark_function(
        smooth_states_weighted_original, 
        states, 
        window=window, 
        alpha=alpha
    )
    
    improved_time = benchmark_function(
        smooth_states_weighted_improved, 
        states, 
        window=window, 
        alpha=alpha
    )
    
    speedup = original_time / improved_time
    
    print(f"\nPerformance comparison for size={size}, window={window}, alpha={alpha}")
    print(f"Original implementation: {original_time:.4f} seconds")
    print(f"Improved implementation: {improved_time:.4f} seconds")
    print(f"Speedup factor: {speedup:.2f}x")
    
    # Verify results match
    original_result = smooth_states_weighted_original(states, window, alpha)
    improved_result = smooth_states_weighted_improved(states, window, alpha)
    np.testing.assert_array_almost_equal(
        original_result, 
        improved_result, 
        decimal=6, 
        err_msg="Results don't match"
    )
    print("Results match")

def test_edge_cases():
    """Test edge cases for both implementations"""
    test_cases = [
        np.array([0]),  # Single element
        np.array([0, 1]),  # Two elements
        np.array([0] * 100),  # Same state
        np.array([0, 1] * 50),  # Alternating states
        np.random.randint(0, 2, 1000)  # Random binary states
    ]
    
    for states in test_cases:
        original_result = smooth_states_weighted_original(states)
        improved_result = smooth_states_weighted_improved(states)
        np.testing.assert_array_almost_equal(
            original_result, 
            improved_result, 
            decimal=6, 
            err_msg=f"Results don't match for case: {states[:10]}..."
        )

if __name__ == "__main__":
    # Run specific performance test
    size = 50000
    window = 10
    alpha = 0.7
    states = generate_test_data(size)
    
    print(f"Running benchmark with size={size}, window={window}, alpha={alpha}")
    
    original_time = benchmark_function(
        smooth_states_weighted_original, 
        states, 
        window=window, 
        alpha=alpha
    )
    
    improved_time = benchmark_function(
        smooth_states_weighted_improved, 
        states, 
        window=window, 
        alpha=alpha
    )
    
    print(f"Original implementation: {original_time:.4f} seconds")
    print(f"Improved implementation: {improved_time:.4f} seconds")
    print(f"Speedup factor: {original_time/improved_time:.2f}x")
