from dotenv import load_dotenv
load_dotenv()

from executor._portfolio import Portfolio

p = Portfolio()
p.initialize()

prd = 'CL'

p.products[prd].position


print(p.products[prd])
p.products[prd].update()
p.products[prd].current_regime
p.products[prd].bars[-1].et
p.products[prd].contracts

x = p.products[prd]

x.current_contract.markPrice
x.current_interval
x.current_bar

x.current_contract.position


for product in p.products.values():
    print(product)
    
    
c = x.contracts['CLQ25']
print(c)