import pytest
import pandas as pd
from hmmer.st_mprocess import run as run_standard
from hmmer.st_mprocess_parallel import run as run_parallel

PRODUCTS = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'KC', 'SB']

@pytest.mark.parametrize("product", PRODUCTS)
def test_compare_run_implementations(product):
    """Test that both implementations of run() produce identical results"""
    
    # Run both implementations with same parameters
    ibarsize = 30
    result_standard = run_standard(product, ibarsize)
    result_parallel = run_parallel(product, ibarsize)
    
    if not result_standard:
        print(f"Standard run failed for product {product}")
        return
    if not result_parallel:
        print(f"Parallel run failed for product {product}")
        return

    # Compare each component of the results
    for key in ['summaries', 'smrybars', 'yearly_summary']:
        try:
            df_standard = result_standard[key]
            df_parallel = result_parallel[key]
            
            # Ensure DataFrames have same sorting
            if 'Date' in df_standard.columns:
                df_standard = df_standard.sort_values('Date').reset_index(drop=True)
                df_parallel = df_parallel.sort_values('Date').reset_index(drop=True)
            elif 'dt' in df_standard.columns:
                df_standard = df_standard.sort_values('dt').reset_index(drop=True)
                df_parallel = df_parallel.sort_values('dt').reset_index(drop=True)
            
            # Compare DataFrames
            try:
                pd.testing.assert_frame_equal(
                    df_standard,
                    df_parallel,
                    check_dtype=False,  # Allow for minor type differences
                    check_exact=False,   # Allow for minor numerical differences
                    rtol=1e-3,          # Relative tolerance for numerical comparisons
                    atol=1e-3           # Absolute tolerance for numerical comparisons
                )
            except AssertionError as e:
                print(f"\nDifferences found in {key} for product {product}:")
                print(str(e))
                print("\nShape comparison:")
                print(f"Standard shape: {df_standard.shape}")
                print(f"Parallel shape: {df_parallel.shape}")
                print("\nColumn comparison:")
                print(f"Standard columns: {df_standard.columns.tolist()}")
                print(f"Parallel columns: {df_parallel.columns.tolist()}")
                
                # If shapes match, show sample of differences
                if df_standard.shape == df_parallel.shape:
                    # Compare numeric columns
                    numeric_cols = df_standard.select_dtypes(include=['float64', 'int64']).columns
                    for col in numeric_cols:
                        if col in df_parallel.columns:
                            diff = (df_standard[col] - df_parallel[col]).abs()
                            if diff.max() > 0:
                                print(f"\nMax difference in {col}: {diff.max()}")
        except KeyError:
            print(f"KeyError: {key} not found in one of the results for product {product}")
