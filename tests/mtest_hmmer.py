import pickle
import pytest
import pandas as pd
import pandas.testing as pdt
from pathlib import Path
import logging

from hmmer.st_mprocess import run
from hmmer.st_mprocess_slowloop import run as srun
from hmmer.st_mprocess_parallel import run as prun

# Configure logging
logging.getLogger('tradertools').setLevel(logging.WARNING)
logging.getLogger('joblib').setLevel(logging.WARNING)

# Setup test data paths
TEST_DATA_DIR = Path("./tests/data")
IBARSIZE = 30

# List of products to test
PRODUCTS = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'KC', 'SB']


def load_baseline_results(product):
    """Load baseline results from pickle file"""
    pkl_path = f"{TEST_DATA_DIR}/results_{product}.pkl"
    with open(pkl_path, "rb") as f:
        res = pickle.load(f)
    
    for key in ['summaries', 'smrybars', 'yearly_summary']:
        if 'prd' in res[key].columns:
            res[key] = res[key].rename(columns={'prd': 'product'})
    return res 

@pytest.fixture(scope="module", params=PRODUCTS)
def all_results(request):
    """Calculate all results once per product"""
    product = request.param
    
    # Load baseline results
    baseline = load_baseline_results(product)
    
    # Calculate all variants
    current = run(product, IBARSIZE)
    parallel = prun(product, IBARSIZE)
    slow = srun(product, IBARSIZE)
    
    # Clean up slow results as needed
    if 'prd' in slow['smrybars']:
        del slow['smrybars']['prd']
    
    return {
        'product': product,
        'baseline': baseline,
        'current': current,
        'parallel': parallel,
        'slow': slow
    }

def compare_results(result1, result2, name1, name2, product):
    """Helper function to compare two result sets"""
    failures = []
    
    for key in ['summaries', 'smrybars', 'yearly_summary']:
        try:
            if isinstance(result1[key], pd.DataFrame):
                pdt.assert_frame_equal(
                    result1[key],
                    result2[key],
                    check_dtype=False,  # Ignore dtype differences
                    rtol=1e-3,  # Relative tolerance
                    atol=1e-5   # Absolute tolerance
                )
            else:
                assert result1[key] == result2[key], f"Mismatch in {key} for {product}"
                
        except AssertionError as e:
            print(f"\nDifferences found in {key} between {name1} and {name2} for {product}:")
            print(str(e))
            
            if isinstance(result1[key], pd.DataFrame):
                differences = result1[key].compare(result2[key])
                print("\nDetailed differences:")
                print(differences)
                print(f"{name1} columns: {result1[key].columns}")
                print(f"{name2} columns: {result2[key].columns}")
            
            failures.append(f"Failed comparison of {key}")
            # Uncomment to fail at first difference:
            # raise
    
    # If we collected any failures, fail the test with a summary
    if failures:
        pytest.fail(f"Found {len(failures)} differences: {', '.join(failures)}")


def test_baseline_current_results(all_results):
    """Test current results against baseline"""
    compare_results(
        all_results['current'],
        all_results['baseline'],
        'current',
        'baseline',
        all_results['product']
    )

def test_baseline_parallel_results(all_results):
    """Test parallel results against baseline"""
    compare_results(
        all_results['parallel'],
        all_results['baseline'],
        'parallel',
        'baseline',
        all_results['product']
    )

def test_baseline_slow_results(all_results):
    """Test slow results against baseline"""
    compare_results(
        all_results['slow'],
        all_results['baseline'],
        'slow',
        'baseline',
        all_results['product']
    )

def test_slow_parallel_results(all_results):
    """Test parallel results against slow"""
    compare_results(
        all_results['parallel'],
        all_results['slow'],
        'parallel',
        'slow',
        all_results['product']
    )

def test_parallel_current_results(all_results):
    """Test current results against parallel"""
    compare_results(
        all_results['current'],
        all_results['parallel'],
        'current',
        'parallel',
        all_results['product']
    )
    
def test_slow_current_results(all_results):
    """Test current results against parallel"""
    compare_results(
        all_results['current'],
        all_results['slow'],
        'current',
        'slow',
        all_results['product']
    )


# import pickle
# import pytest
# import pandas as pd
# import pandas.testing as pdt
# from pathlib import Path
# import logging

# from hmmer.st_mprocess import run
# from hmmer.st_mprocess_slowloop import run as srun
# from hmmer.st_mprocess_parallel import run as prun

# # Configure logging
# logging.getLogger('tradertools').setLevel(logging.WARNING)
# logging.getLogger('joblib').setLevel(logging.WARNING)

# # Setup test data paths
# TEST_DATA_DIR = Path("./tests/data")
# IBARSIZE = 30

# # List of products to test
# PRODUCTS = ['CL', 'NG', 'ZC', 'ZS', 'ZW', 'HG', 'GC', 'NQ', 'KC', 'SB']

# def load_baseline_results(product):
#     """Load baseline results from pickle file"""
#     pkl_path = f"{TEST_DATA_DIR}/results_{product}.pkl"
#     with open(pkl_path, "rb") as f:
#         res = pickle.load(f)
    
#     for key in ['summaries', 'smrybars', 'yearly_summary']:
#         if 'prd' in res[key].columns:
#             res[key] = res[key].rename(columns={'prd': 'product'})
#     return res 

# @pytest.mark.parametrize("product", PRODUCTS)
# def test_baseline_current_results(product, ibarsize=IBARSIZE):
#     """Test results for each product against baseline"""
#     # Load baseline results
#     baseline = load_baseline_results(product)
#     current = run(product, ibarsize)
    
#     for key in ['summaries', 'smrybars', 'yearly_summary']:
#         try:
#             if isinstance(baseline[key], pd.DataFrame):
#                 pdt.assert_frame_equal(
#                     current[key],
#                     baseline[key],
#                     check_dtype=False,  # Ignore dtype differences
#                     rtol=1e-3,  # Relative tolerance
#                     atol=1e-5   # Absolute tolerance
#                 )
#             else:
#                 assert current[key] == baseline[key], f"Mismatch in {key} for {product}"
                
#         except AssertionError as e:
#             print(f"\nDifferences found in {key} for {product}:")
#             print(str(e))
            
#             # Save differences for investigation if they're DataFrames
#             if isinstance(baseline[key], pd.DataFrame):
#                 differences = current[key].compare(baseline[key])
#                 print("\nDetailed differences:")
#                 print(differences)
            
#             raise e 

# @pytest.mark.parametrize("product", PRODUCTS)
# def test_baseline_parallel_results(product, ibarsize=IBARSIZE):
#     """Test results for each product against baseline"""
#     # Load baseline results
#     baseline = load_baseline_results(product)
#     parallel = prun(product, ibarsize)
    
#     for key in ['summaries', 'smrybars', 'yearly_summary']:
#         try:
#             if isinstance(baseline[key], pd.DataFrame):
#                 pdt.assert_frame_equal(
#                     parallel[key],
#                     baseline[key],
#                     check_dtype=False,  # Ignore dtype differences
#                     rtol=1e-3,  # Relative tolerance
#                     atol=1e-5   # Absolute tolerance
#                 )
#             else:
#                 assert parallel[key] == baseline[key], f"Mismatch in {key} for {product}"
                
#         except AssertionError as e:
#             print(f"\nDifferences found in {key} for {product}:")
#             print(str(e))
            
#             # Save differences for investigation if they're DataFrames
#             if isinstance(baseline[key], pd.DataFrame):
#                 differences = parallel[key].compare(baseline[key])
#                 print("\nDetailed differences:")
#                 print(differences)
            
#             raise e 
        
        
# @pytest.mark.parametrize("product", PRODUCTS)
# def test_baseline_slow_results(product, ibarsize=IBARSIZE):
#     """Test results for each product against baseline"""
#     # Load baseline results
#     baseline = load_baseline_results(product)
#     slow = srun(product, ibarsize)
    
#     del slow['smrybars']['prd']
    
#     for key in ['summaries', 'smrybars', 'yearly_summary']:
#         try:
#             if isinstance(slow[key], pd.DataFrame):
#                 pdt.assert_frame_equal(
#                     baseline[key],
#                     slow[key],
#                     check_dtype=False,  # Ignore dtype differences
#                     rtol=1e-3,  # Relative tolerance
#                     atol=1e-5   # Absolute tolerance
#                 )
#             else:
#                 assert baseline[key] == slow[key], f"Mismatch in {key} for {product}"
                
#         except AssertionError as e:
#             print(f"\nDifferences found in {key} for {product}:")
#             print(str(e))
            
#             differences = baseline[key].compare(slow[key])
#             print("\nDetailed differences:")
#             print(differences)
#             print(f"bcols: {baseline[key].columns}")
#             print(f"scols: {slow[key].columns}")
            
#             # Save differences for investigation if they're DataFrames
#             # if isinstance(slow[key], pd.DataFrame):
            
#             raise e
        


# @pytest.mark.parametrize("product", PRODUCTS)
# def test_slow_parallel_results(product, ibarsize=IBARSIZE):
#     slow = srun(product, ibarsize)
#     parallel = prun(product, ibarsize)
    
#     del slow['smrybars']['prd']
    
#     for key in ['summaries', 'smrybars', 'yearly_summary']:
#         try:
#             if isinstance(slow[key], pd.DataFrame):
#                 pdt.assert_frame_equal(
#                     parallel[key],
#                     slow[key],
#                     check_dtype=False,  # Ignore dtype differences
#                     rtol=1e-3,  # Relative tolerance
#                     atol=1e-5   # Absolute tolerance
#                 )
#             else:
#                 assert parallel[key] == slow[key], f"Mismatch in {key} for {product}"
                
#         except AssertionError as e:
#             print(f"\nDifferences found in {key} for {product}:")
#             print(str(e))
            
#             differences = parallel[key].compare(slow[key])
#             print("\nDetailed differences:")
#             print(differences)
#             print(f"pcols: {parallel[key].columns}")
#             print(f"scols: {slow[key].columns}")
            
#             # Save differences for investigation if they're DataFrames
#             # if isinstance(slow[key], pd.DataFrame):
            
#             raise e
            
            

        
# @pytest.mark.parametrize("product", PRODUCTS)
# def test_parallel_current_results(product, ibarsize=IBARSIZE):
#     # Load baseline results
#     current = run(product, ibarsize)
#     parallel = prun(product, ibarsize)
    
#     for key in ['summaries', 'smrybars', 'yearly_summary']:
#         try:
#             if isinstance(current[key], pd.DataFrame):
#                 pdt.assert_frame_equal(
#                     current[key],
#                     parallel[key],
#                     check_dtype=False,  # Ignore dtype differences
#                     rtol=1e-3,  # Relative tolerance
#                     atol=1e-5   # Absolute tolerance
#                 )
#             else:
#                 assert parallel[key] == current[key], f"Mismatch in {key} for {product}"
                
#         except AssertionError as e:
#             print(f"\nDifferences found in {key} for {product}:")
#             print(str(e))
            
#             differences = parallel[key].compare(current[key])
#             print("\nDetailed differences:")
#             print(differences)
#             print(f"bcols: {parallel[key].columns}")
#             print(f"scols: {current[key].columns}")
            
#             # Save differences for investigation if they're DataFrames
#             # if isinstance(slow[key], pd.DataFrame):
            
#             raise e
