# %%

# from datetime import datetime
import os 
print(os.getcwd() )
import pandas as pd
import tradertools as tt


pg = tt.clients.postgres.PostgresClient('test', **{
    "host": "localhost",
    "port": 5432,
    "user": "jack",
    "password": "jack",
    "database": "main",
})


products = {
    "NG": "QNG"
}
rproducts = { v: k for k, v in products.items()}

pkl_data = pd.DataFrame()
for p in products:
    pkl_data: pd.DataFrame = pd.read_pickle(f"./tests/data/idata_{p}.pkl")
    pkl_data['dt'] = pkl_data['dt'].astype(object)
    pkl_data['dt'] = pkl_data['dt'].apply(lambda x: x.to_pydatetime())
    


symbols = pkl_data['name2'].unique()


query_params = []

for s in symbols:
    p = s[:-3]
    start_time = pkl_data[pkl_data['name2'] == s]['dt'].min()
    end_time = pkl_data[pkl_data['name2'] == s]['dt'].max()
    query_params.append((products[p] + s[-3:], start_time, end_time))
    print(query_params[-1])
    



params = query_params[0]
x = tt.marketdata.historical.get_bars_in_period(params[0], 60*30, params[1].to_pydatetime(), params[2].to_pydatetime())
iq_df = pd.DataFrame(x[1:], columns=x[0])
iq_df = iq_df[::-1]
iq_df.columns = ['dt', 'Open', 'High', 'Low', 'Close', 'volume', "period_volume", "num_trades"]
iq_df['dt'] = pd.to_datetime(iq_df['dt'])
iq_df = iq_df.set_index('dt')

symbol = rproducts[params[0][0:3]] + params[0][-3:]
pkl_df = pkl_data.query(f"name2 == '{symbol}'")
pkl_df = pkl_df.set_index('dt')

# %%



# Option 3: Select only the Close columns
comparison = pkl_df[['Close']].join(iq_df[['Close']], rsuffix='_iq')
comparison['diff'] = comparison['Close'] - comparison['Close_iq']


# %%

import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Create subplots with 2 rows
fig = make_subplots(
    rows=2, cols=1,
    subplot_titles=('Close Prices Comparison', 'Difference'),
    vertical_spacing=0.08
)

# Add the two Close price series to the top subplot
fig.add_trace(
    go.Scatter(x=comparison.index, y=comparison['Close'], name='PKL Close', line=dict(color='blue')),
    row=1, col=1
)

fig.add_trace(
    go.Scatter(x=comparison.index, y=comparison['Close_iq'], name='IQ Close', line=dict(color='red')),
    row=1, col=1
)

# Add the difference to the bottom subplot
fig.add_trace(
    go.Scatter(x=comparison.index, y=comparison['diff'], name='Difference', line=dict(color='green')),
    row=2, col=1
)

# Update layout
fig.update_layout(
    title=f"Close Price Comparison for {symbol}",
    height=600,
    showlegend=True
)

# Update y-axis labels
fig.update_yaxes(title_text="Price", row=1, col=1)
fig.update_yaxes(title_text="Difference", row=2, col=1)
fig.update_xaxes(title_text="Time", row=2, col=1)

fig.show()
# %%
