import time
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo


# import joblib
import numpy as np
import pandas as pd
from pandas.tseries.offsets import CustomBusinessDay
from pandas.tseries.holiday import USFederalHolidayCalendar

from hmmer import db
from hmmer.ohlc_utils2 import calculate_expiry_dates



STARTING_YEAR = 2018

# Total number of months to get data for per contract
CT_MNTHS_BACK = 18

# Number of jobs for parallel processing
# N_JOBS = 16


prd = 'NG'
n_jobs = 1
record_results = False

timings = []
start_time = time.perf_counter()

p = db.get_parameter(prd)

shiftdays = p.shiftdays
shifttime = p.shifttime

fd1_window = p.fd1_window
fd1_d = p.fd1_d
fd2_window = p.fd2_window
fd2_d = p.fd2_d

norm_position_window = p.norm_position_window
smoothing_window = p.smoothing_window
smoothing_alpha = p.smoothing_alpha
mthsback = p.mthsback
covariance_type = p.covariance_type
window_size = p.window_size
sticky_factor = p.sticky_factor
forward_bars = p.forward_bars

# stop = p.stop
# stopadj = p.stopadj
# trail = p.trail

open_time = p.eopen_est
# close_time = p.eclose_est
runtimestamp = datetime.now().astimezone(ZoneInfo("America/New_York"))
us_bd = CustomBusinessDay(calendar=USFederalHolidayCalendar())

#Get DTN data from database
stime = time.perf_counter()
# years_back = mthsback // 12 + 0
months_back = mthsback + 6
# years_back = 10
# idata  = db.load_hmmdb_data(prd, runtimestamp, shifttime, ct_mnths_back=CT_MNTHS_BACK, years_back=years_back)
idata  = db.load_hmmdb_data(prd, runtimestamp, shifttime, ct_mnths_back=CT_MNTHS_BACK, months_back=months_back)
timings.append(('load_data', round(time.perf_counter() - stime, 2)))


##### masking #####
stime = time.perf_counter()

# Pre-compute all datetime components once
dt = idata['dt']
dt_dates = dt.dt.date
dt_times = dt.dt.time
weekdays = dt.dt.weekday
is_sunday = weekdays == 6

# Original Sunday filtering
sunday_early_mask = is_sunday & (dt_times < open_time)
idata = idata[~sunday_early_mask]

# Only recompute what changed after filtering
dt_dates = idata['dt'].dt.date
dt_times = idata['dt'].dt.time
is_sunday = idata['dt'].dt.weekday == 6

# Initialize TradeDate
idata['TradeDate'] = dt_dates
most_recent_date = dt_dates.max()

# Combined shift logic - avoid duplicate operations
needs_shift_mask = (
    (dt_times >= shifttime) |  # after TAS, any weekday
    (is_sunday & (dt_times >= open_time))  # Sunday evening
)

# Split into historical vs recent for different handling
is_historical = dt_dates < most_recent_date
historical_shift = needs_shift_mask & is_historical
recent_shift = needs_shift_mask & ~is_historical

# Build next_day mapping only once, only if needed
next_day_map = None
def get_next_day_map():
    global next_day_map
    if next_day_map is None:
        unique_dates = sorted(idata['TradeDate'].unique())
        next_day_map = {
            d: unique_dates[i + 1] if i < len(unique_dates) - 1 else d
            for i, d in enumerate(unique_dates)
        }
    return next_day_map

# Process shifts
if historical_shift.any():
    idata.loc[historical_shift, 'TradeDate'] = idata.loc[historical_shift, 'TradeDate'].map(get_next_day_map())

if recent_shift.any():
    dates_to_shift = pd.to_datetime(idata.loc[recent_shift, 'TradeDate'])
    idata.loc[recent_shift, 'TradeDate'] = (dates_to_shift + us_bd).dt.date

# Handle Sunday TradeDates in one pass
# Use vectorized weekday check instead of apply
tradedate_series = pd.to_datetime(idata['TradeDate'])
sunday_tradedate_mask = tradedate_series.dt.weekday == 6

if sunday_tradedate_mask.any():
    historical_sunday = sunday_tradedate_mask & is_historical
    recent_sunday = sunday_tradedate_mask & ~is_historical
    
    if historical_sunday.any():
        idata.loc[historical_sunday, 'TradeDate'] = idata.loc[historical_sunday, 'TradeDate'].map(get_next_day_map())
    
    if recent_sunday.any():
        sunday_dates = tradedate_series[recent_sunday]
        idata.loc[recent_sunday, 'TradeDate'] = (sunday_dates + us_bd).dt.date

timings.append(('masking', round(time.perf_counter() - stime, 2)))
##### /masking #####

# products_with_change_days = [(prds[0], shiftdays)]
stime = time.perf_counter()
# idata = add_expiry_position(idata,
#                             #  products_with_change_days,
#                             shiftdays,
#                             trade_date_col='TradeDate',
#                             #  product_col='prd',
#                             trade_year_col='expiry_yr',
#                             trade_month_col='expiry_mth')

trade_date_col='TradeDate'
trade_year_col='expiry_yr'
trade_month_col='expiry_mth'

idata[trade_date_col] = pd.to_datetime(idata[trade_date_col]).dt.normalize()

# Create the contract code column ("exp_yr_m") by concatenating the year and month fields
idata['exp_yr_m'] = pd.to_datetime(
    idata[trade_year_col].astype(int).astype(str) + '-' +
    idata[trade_month_col].astype(int).astype(str).str.zfill(2) + '-01'
)
    
start_date = idata[trade_date_col].min()
end_date = idata[trade_date_col].max() + timedelta(days=10)

num_columns = 20  # number of forward expiry dates to compute

# Get the unique expiry month numbers
expiry_months = np.sort(idata[trade_month_col].unique())

# Compute the "exit months"
exit_mths = sorted({
    (pd.Timestamp(year=2000, month=int(m), day=1) + pd.Timedelta(days=shiftdays)).month
    for m in expiry_months
})
expiry_df = calculate_expiry_dates(start_date, end_date, shiftdays, exit_mths, num_columns)


expiry_long = expiry_df.melt(id_vars='Date', var_name='fwdper', value_name='exp_yr_m')
expiry_long['fwdper'] = expiry_long['fwdper'].str.replace('V', '').astype(int)
expiry_long = expiry_long.rename(columns={'Date': trade_date_col})

merged = pd.merge(idata, expiry_long, on=[trade_date_col, 'exp_yr_m'], how='left')
merged = merged[~merged['fwdper'].isna()].copy()
merged['fwdper'] = merged['fwdper'].astype(int)

merged = merged.drop('exp_yr_m', axis=1).reset_index(drop=True)


i = 900
merged[merged['fwdper']==1].iloc[i:i+100]


fmerged = merged[merged['fwdper']==1]
fmerged.reset_index(drop=True, inplace=True)

# Find rows where name2 is different from the previous row
contract_changes = fmerged['name2'] != fmerged['name2'].shift(1)

# Get the rows where contract changes occur
change_rows = fmerged[contract_changes]

# Or if you want the index positions
change_indices = fmerged[contract_changes].index.tolist()

transitions = fmerged[contract_changes].copy()
transitions['prev_name2'] = fmerged['name2'].shift(1)[contract_changes]

print(transitions[['dt', 'name2', 'prev_name2', 'fwdper']])

# Get indices where contract changes
change_indices = fmerged[fmerged['name2'] != fmerged['name2'].shift(1)].index

# Show context around each change
for idx in change_indices:
    if idx > 0:  # Make sure we have a previous row
        context = fmerged.iloc[idx-1:idx+1][['dt', 'TradeDate', 'name2', 'fwdper']]
        print(f"\nContract change at index {idx}:")
        print(context)

timings.append(('add_expiry_position', round(time.perf_counter() - stime, 2)))