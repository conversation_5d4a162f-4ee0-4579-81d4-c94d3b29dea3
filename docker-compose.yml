services:
  downloader:
    image: ghcr.io/luneanalytics/hmmer_downloader:latest
    container_name: hmmer_downloader
    network_mode: host
    volumes:
      - ./logs:/app/logs
      - ./connections.yaml:/app/connections.yaml:ro
      - ./.env:/app/.env:ro
      - ./config/config.yaml:/app/config/config.yaml:ro
    env_file:
      - .env
    restart: "no"

  modeler:
    image: ghcr.io/luneanalytics/hmmer_modeler:latest
    container_name: hmmer_modeler
    network_mode: host
    volumes:
      - ./logs:/app/logs
      - ./connections.yaml:/app/connections.yaml:ro
      - ./.env:/app/.env:ro
      - ./config/config.yaml:/app/config/config.yaml:ro
    env_file:
      - .env
    depends_on:
      downloader:
        condition: service_completed_successfully
    restart: "no"

  executor:
    image: ghcr.io/luneanalytics/hmmer_executor:latest
    container_name: hmmer_executor
    network_mode: host
    environment:
      - TZ=America/New_York
    volumes:
      - ./logs:/app/logs
      - ./connections.yaml:/app/connections.yaml:ro
      - ./.env:/app/.env:ro
      - ./config/config.yaml:/app/config/config.yaml:ro
      - ./config/ib.yaml:/app/config/ib.yaml:ro
    env_file:
      - .env
    restart: "no"