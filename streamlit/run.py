#!/usr/bin/env python
"""
Script to run the Streamlit server for the HMMER project.

Usage:
    poetry run db
    python streamlit/run.py
    python streamlit/run.py --port 8502 --host 0.0.0.0
"""
import sys
import argparse
import subprocess
from pathlib import Path

def get_app_path():
    """Get the absolute path to the Streamlit app.py file."""
    return Path(__file__).parent / "app.py"

def run_streamlit(port=8501, host="localhost", no_browser=False):
    """Run the Streamlit server.

    Args:
        port (int): Port to run the server on
        host (str): Host to run the server on  
        no_browser (bool): Whether to skip opening the browser
    """
    # Get the absolute path to the app.py file
    app_path = get_app_path()

    # Ensure the path exists
    if not app_path.exists():
        print(f"Error: Could not find {app_path}")
        sys.exit(1)

    # Print startup message
    print(f"Starting HMMER Streamlit server on http://{host}:{port}")

    # Build the command
    cmd = [
        "streamlit", "run",
        str(app_path),
        "--server.port", str(port),
        "--server.address", host,
    ]

    if no_browser:
        cmd.extend(["--browser.gatherUsageStats", "false"])

    # Run the command
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nStreamlit server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error running Streamlit: {e}")
        sys.exit(1)

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Run the HMMER Streamlit server")
    parser.add_argument("--port", type=int, default=8501, help="Port to run the server on")
    parser.add_argument("--host", type=str, default="localhost", help="Host to run the server on")
    parser.add_argument("--no-browser", action="store_true", help="Don't open browser automatically")

    args = parser.parse_args()
    run_streamlit(port=args.port, host=args.host, no_browser=args.no_browser)

if __name__ == "__main__":
    main()