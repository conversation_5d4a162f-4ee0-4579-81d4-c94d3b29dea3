import os
import sys
from pathlib import Path

# Add the src directory to the Python path so we can import hmmer modules
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import plotly.graph_objects as go


import streamlit as st
import pandas as pd

PG_CLIENT = os.getenv("POSTGRES_CLIENT")
NATS_CLIENT = os.getenv("NATS_CLIENT")

ROW_HEIGHT = 20

try:
    from executor._portfolio import Portfolio
    # from executor._productgroup import ProductGroup
    # from executor.types.enums import Side, ProductState
except ImportError as e:
    st.error(f"Could not import HMMER modules: {e}")
    st.stop()
    
if 'nats' not in st.session_state:
    from executor._natsinterface import NatsInterface
    st.session_state['nats'] = NatsInterface(NATS_CLIENT)
nats = st.session_state['nats'].nats
    
if 'pg' not in st.session_state:
    from tradertools.clients import get_pg_client
    st.session_state['pg'] = get_pg_client(PG_CLIENT)
pg = st.session_state['pg']
    

if 'p' not in st.session_state:
    p = Portfolio()
    p.initialize()
    st.session_state['p'] = p
p = st.session_state['p']

st.set_page_config(
    page_title="HMMER Dashboard",
    page_icon="📈",
    layout="wide",
)



st.warning("SIMULATION")

summary = None
products = None
openOprders = None
prd_names = None

@st.fragment(run_every=60)
def live_data():
    global summary, products, openOrders, prd_names
    summary = nats.request('strategies.hmmer.requests', 'summary')
    products = pd.DataFrame(nats.request('strategies.hmmer.requests', 'summary_products')).set_index('product')
    openOrders = pd.DataFrame(nats.request('strategies.hmmer.requests', 'openOrders')).set_index('orderId')
    fills = pd.DataFrame(nats.request('strategies.hmmer.requests', 'fills')).set_index('datetime')
    prd_names = products.index.unique().tolist()
        

    title_col, pl_col = st.columns([4, 2])
    with title_col:
        st.subheader("🎯 HMMER Trading Dashboard")
    with pl_col:
        st.subheader(f"Lifetime PL: ${summary['pl']:.2f}")

    st.subheader("Products")
    st.dataframe(products, row_height=ROW_HEIGHT)

    st.subheader("Open Orders")
    st.dataframe(openOrders, row_height=ROW_HEIGHT)
    
    st.subheader("Fills")
    st.dataframe(fills, row_height=ROW_HEIGHT)
    
    
live_data()

param_col, data_col = st.columns([1, 5])

with param_col:
    st.selectbox("Select Product", prd_names, key='selected_product') # type: ignore
    st.subheader("Parameters")
    params = pd.DataFrame(pg.query(f"SELECT * FROM hmmer.parameters WHERE product = '{st.session_state['selected_product']}'"))
    del params['created_at']
    del params['updated_at']
    st.dataframe(params.T, row_height=ROW_HEIGHT)
with data_col:
    chart_tab, data_tab = st.tabs(["Chart", "Data"])
    with chart_tab:
        bars_raw = p.products[st.session_state['selected_product']].bars.df[['open', 'high', 'low', 'close', 'regime']]
        bars = bars_raw.reset_index()  # This should give you a 'datetime' column
    
        # Assuming the datetime column is called 'datetime' or 'index'
        datetime_col = bars.columns[0]  # First column should be datetime
        bars = bars.rename(columns={datetime_col: 'datetime'})
        
        # Create the figure
        fig = go.Figure()
        
        # Add close price line
        fig.add_trace(go.Scatter(
            x=bars['datetime'],
            y=bars['close'],
            mode='lines',
            name='Close Price',
            line=dict(color='blue', width=2)
        ))
        
        # Add regime shading
        regime_colors = {
            -1: 'rgba(255, 0, 0, 0.2)',    # Red for short regime
            0: 'rgba(128, 128, 128, 0.2)',  # Gray for neutral regime  
            1: 'rgba(0, 255, 0, 0.2)'     # Green for long regime
        }
        
        regime_labels = {
            -1: 'Short',
            0: 'Neutral',
            1: 'Long'
        }
        
        # Group consecutive periods with same regime
        regime_changes = bars['regime'].ne(bars['regime'].shift()).cumsum()
        regime_groups = bars.groupby(regime_changes)
        
        # Track which regimes we've added to legend
        added_to_legend = set()
        
        # Get the price range for proper shading
        y_min = bars['close'].min()
        y_max = bars['close'].max()
        y_range = y_max - y_min
        y_padding = y_range * 0.05  # 5% padding
        
        for _, group in regime_groups:
            regime_value = group['regime'].iloc[0]
            if regime_value in regime_colors:
                # Add to legend only once per regime
                show_legend = regime_value not in added_to_legend
                if show_legend:
                    added_to_legend.add(regime_value)
                
                fig.add_shape(
                    type="rect",
                    x0=group['datetime'].iloc[0],
                    x1=group['datetime'].iloc[-1],
                    y0=y_min - y_padding,  # Use actual data range
                    y1=y_max + y_padding,  # Use actual data range
                    fillcolor=regime_colors[regime_value],
                    layer="below",
                    line_width=0,
                )
                
                # Add invisible trace for legend
                if show_legend:
                    fig.add_trace(go.Scatter(
                        x=[None], 
                        y=[None],
                        mode='markers',
                        marker=dict(
                            size=10, 
                            color=regime_colors[regime_value].replace('0.2', '0.8'),
                            symbol='square'
                        ),
                        name=f"Regime: {regime_labels[regime_value]}",
                        showlegend=True
                    ))
        
        # Update layout with auto-scaling
        fig.update_layout(
            title=f"{st.session_state['selected_product']} - Close Price with Regime Overlay",
            xaxis_title="Date",
            yaxis_title="Price",
            showlegend=True,
            hovermode='x unified',
            width=800,
            height=400,
            yaxis=dict(
                autorange=True,  # Enable auto-scaling
                fixedrange=False  # Allow zoom/pan on y-axis
            )
        )
        
        # Add range selector and slider
        fig.update_layout(
            xaxis=dict(
                rangeselector=dict(
                    buttons=list([
                        dict(count=1, label="1M", step="month", stepmode="backward"),
                        dict(count=6, label="6M", step="month", stepmode="backward"),
                        dict(count=1, label="1Y", step="year", stepmode="backward"),
                        dict(step="all")
                    ])
                ),
                rangeslider=dict(visible=True),
                type="date"
            )
        )
        
        st.plotly_chart(fig, use_container_width=True)
    with data_tab:
        st.dataframe(p.products[st.session_state['selected_product']].bars.df, row_height=ROW_HEIGHT)