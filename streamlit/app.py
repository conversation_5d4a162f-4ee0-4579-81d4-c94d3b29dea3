#!/usr/bin/env python
"""
HMMER Streamlit Dashboard

Main entry point for the Streamlit web interface.
"""
import sys
from pathlib import Path

# Add the src directory to the Python path so we can import hmmer modules
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import plotly.graph_objects as go


import streamlit as st
import pandas as pd

# Now you can import your hmmer modules
try:
    from executor._portfolio import Portfolio
    # from executor._productgroup import ProductGroup
    # from executor.types.enums import Side, ProductState
except ImportError as e:
    st.error(f"Could not import HMMER modules: {e}")
    st.stop()

if 'p' not in st.session_state:
    p = Portfolio()
    p.initialize()
    st.session_state['p'] = p
    
if 'nats' not in st.session_state:
    from executor._natsinterface import NatsInterface
    nats = NatsInterface("nats_dev")
    st.session_state['nats'] = nats

def main():
    """Main Streamlit app."""
    
    st.set_page_config(
        page_title="HMMER Dashboard",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("🎯 HMMER Trading Dashboard")
    
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox("Choose a page", [
        "Overview", 
        "Portfolio", 
        "Product Groups", 
        "Orders",
        "Settings"
    ])
    
    if page == "Overview":
        show_overview()
    elif page == "Portfolio":
        show_portfolio()
    elif page == "Product Groups":
        show_product_groups()
    elif page == "Orders":
        show_orders()
    elif page == "Settings":
        show_settings()

def show_overview():
    """Show overview page."""
    st.header("📊 Overview")
    
    # Metrics row
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total P&L", "$1,234.56", "12.3%")
    with col2:
        st.metric("Open Positions", "5", "1")
    with col3:
        st.metric("Active Orders", "3", "-2")
    with col4:
        st.metric("Daily Volume", "150", "23")

def show_portfolio():
    """Show portfolio page."""
    st.header("💼 Portfolio")
    df = pd.DataFrame(st.session_state['p'].summary_rows())
    df.set_index('product', inplace=True)
    st.dataframe(df)


def show_product_groups():
    """Show product groups page."""
    st.header("📦 Product Groups")
    st.write("Product group management and monitoring will go here.")
    p = st.session_state['p']
    products = sorted(list(p.products.keys()))
    selected_product = st.sidebar.selectbox("Select Product Group", options=products)
    
    # Get the data with proper datetime index
    bars_raw = p.products[selected_product].bars.df[['open', 'high', 'low', 'close', 'regime']]
    bars = bars_raw.reset_index()  # This should give you a 'datetime' column
    
    # Assuming the datetime column is called 'datetime' or 'index'
    datetime_col = bars.columns[0]  # First column should be datetime
    bars = bars.rename(columns={datetime_col: 'datetime'})
    
    # Create the figure
    fig = go.Figure()
    
    # Add close price line
    fig.add_trace(go.Scatter(
        x=bars['datetime'],
        y=bars['close'],
        mode='lines',
        name='Close Price',
        line=dict(color='blue', width=2)
    ))
    
    # Add regime shading
    regime_colors = {
        -1: 'rgba(255, 0, 0, 0.2)',    # Red for short regime
         0: 'rgba(128, 128, 128, 0.2)',  # Gray for neutral regime  
         1: 'rgba(0, 255, 0, 0.2)'     # Green for long regime
    }
    
    regime_labels = {
        -1: 'Short',
         0: 'Neutral',
         1: 'Long'
    }
    
    # Group consecutive periods with same regime
    regime_changes = bars['regime'].ne(bars['regime'].shift()).cumsum()
    regime_groups = bars.groupby(regime_changes)
    
    # Track which regimes we've added to legend
    added_to_legend = set()
    
    # Get the price range for proper shading
    y_min = bars['close'].min()
    y_max = bars['close'].max()
    y_range = y_max - y_min
    y_padding = y_range * 0.05  # 5% padding
    
    for _, group in regime_groups:
        regime_value = group['regime'].iloc[0]
        if regime_value in regime_colors:
            # Add to legend only once per regime
            show_legend = regime_value not in added_to_legend
            if show_legend:
                added_to_legend.add(regime_value)
            
            fig.add_shape(
                type="rect",
                x0=group['datetime'].iloc[0],
                x1=group['datetime'].iloc[-1],
                y0=y_min - y_padding,  # Use actual data range
                y1=y_max + y_padding,  # Use actual data range
                fillcolor=regime_colors[regime_value],
                layer="below",
                line_width=0,
            )
            
            # Add invisible trace for legend
            if show_legend:
                fig.add_trace(go.Scatter(
                    x=[None], 
                    y=[None],
                    mode='markers',
                    marker=dict(
                        size=10, 
                        color=regime_colors[regime_value].replace('0.2', '0.8'),
                        symbol='square'
                    ),
                    name=f"Regime: {regime_labels[regime_value]}",
                    showlegend=True
                ))
    
    # Update layout with auto-scaling
    fig.update_layout(
        title=f"{selected_product} - Close Price with Regime Overlay",
        xaxis_title="Date",
        yaxis_title="Price",
        showlegend=True,
        hovermode='x unified',
        width=800,
        height=400,
        yaxis=dict(
            autorange=True,  # Enable auto-scaling
            fixedrange=False  # Allow zoom/pan on y-axis
        )
    )
    
    # Add range selector and slider
    fig.update_layout(
        xaxis=dict(
            rangeselector=dict(
                buttons=list([
                    dict(count=1, label="1M", step="month", stepmode="backward"),
                    dict(count=6, label="6M", step="month", stepmode="backward"),
                    dict(count=1, label="1Y", step="year", stepmode="backward"),
                    dict(step="all")
                ])
            ),
            rangeslider=dict(visible=True),
            type="date"
        )
    )
    
    st.plotly_chart(fig, use_container_width=True)
    st.dataframe(bars)
    
# # altair
# def show_product_groups():
#     """Show product groups page."""
#     st.header("📦 Product Groups")
#     st.write("Product group management and monitoring will go here.")
#     p = st.session_state['p']
#     products = sorted(list(p.products.keys()))
#     selected_product = st.sidebar.selectbox("Select Product Group", options=products)
    
#     # Get the data with proper datetime index
#     bars_raw = p.products[selected_product].bars.df[['close', 'regime']]
#     bars = bars_raw.reset_index()  # This should give you a 'datetime' column
    
#     # Check what the datetime column is called
#     st.write("Columns:", bars.columns.tolist())  # Debug line - remove later
    
#     # Assuming the datetime column is called 'datetime' or 'index'
#     datetime_col = bars.columns[0]  # First column should be datetime
#     bars = bars.rename(columns={datetime_col: 'datetime'})
    
#     # Prepare data for regime shading
#     regime_changes = bars['regime'].ne(bars['regime'].shift()).cumsum()
#     bars['regime_group'] = regime_changes
    
#     # Create regime segments for background shading
#     regime_segments = []
#     for group_id in bars['regime_group'].unique():
#         group_data = bars[bars['regime_group'] == group_id]
#         regime_segments.append({
#             'start': group_data['datetime'].min(),
#             'end': group_data['datetime'].max(),
#             'regime': group_data['regime'].iloc[0]
#         })
    
#     regime_df = pd.DataFrame(regime_segments)
    
#     # Define regime colors
#     regime_color_scale = alt.Scale(
#         domain=[-1, 0, 1],
#         range=['red', 'gray', 'green']
#     )
    
#     # Background regime shading
#     background = alt.Chart(regime_df).mark_rect(
#         opacity=0.3
#     ).encode(
#         x=alt.X('start:T'),
#         x2=alt.X2('end:T'),
#         color=alt.Color(
#             'regime:O',
#             scale=regime_color_scale,
#             legend=alt.Legend(
#                 title="Regime",
#                 symbolType="square",
#                 labelExpr="datum.value == -1 ? 'Short' : datum.value == 0 ? 'Neutral' : 'Long'"
#             )
#         )
#     )
    
#     # Close price line
#     line = alt.Chart(bars).mark_line(
#         color='blue',
#         strokeWidth=2
#     ).encode(
#         x=alt.X('datetime:T', title='Date'),
#         y=alt.Y('close:Q', title='Price')
#     )
    
#     # Combine layers
#     chart = alt.layer(
#         background,
#         line
#     ).resolve_scale(
#         color='independent'
#     ).properties(
#         width=800,
#         height=400,
#         title=f"{selected_product} - Close Price with Regime Overlay"
#     ).add_params(
#         alt.selection_interval(bind='scales')
#     )
    
#     st.altair_chart(chart, use_container_width=True)
#     st.dataframe(bars)



def show_orders():
    """Show orders page."""
    st.header("📋 Orders")
    nats = st.session_state['nats']
    orders = nats.get('orders')
    st.dataframe(pd.DataFrame(orders))

def show_settings():
    """Show settings page."""
    st.header("⚙️ Settings")
    st.write("Configuration and settings will go here.")

if __name__ == "__main__":
    main()