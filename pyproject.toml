[tool.poetry]
name = "hmmer"
version = "0.1.0"
description = ""
authors = ["LoopyJack <<EMAIL>>"]
readme = "README.md"
packages = [{include = "hmmer", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.12,<3.14"
setproctitle = ">=1.3.5,<2.0.0"
python-dotenv = ">=1.1.0,<2.0.0"
tqdm = "^4.67.1"
pandas = "^2.2.3"
hmmlearn = "^0.3.3"
pyarrow = "^19.0.1"
tradertools = {git = "https://github.com/LuneAnalytics/tradertools.git"}
sqlalchemy = "^2.0.40"
matplotlib = "^3.9.0"
nbformat = "^5.10.4"
numba = "^0.61.2"
ibapi-wrapper = {git = "https://github.com/LuneAnalytics/ibapi-wrapper"}
python-dateutil = "^2.9.0.post0"
requests = "^2.32.5"
psutil = "^7.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
black = "^25.1.0"
isort = "^6.0.1"
flake8 = "^7.2.0"
mypy = "^1.15.0"
ipykernel = "^6.29.5"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
ignore = [
    "E402", # module level import not at top of file
    "F403", # while expected an indented block
]